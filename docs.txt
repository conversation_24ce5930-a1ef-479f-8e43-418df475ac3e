# API Connection:

API-Key: ct-db50b940-8fc7-42dc-83e8-330ac421e288

### simple post request with body to:

```
fetch(https://chat-translator-backend-test.onrender.com/chatcompletion, {
    method: "POST",
    headers: {
    "Content-Type": "application/json",
        "x-api-key": API_KEY!,
    },
    body: JSON.stringify(body),
});
```

### Response:

```
{
    resText: string,
    alert: string,
    summary?: {
        user?: string,
        assistant?: string
    }
}
```

### structure of the body:

```
const body = {
    siteInfos: SiteInfos,
}
```

## The Siteinfos

The important part is to get the siteInfos right. Following is the structure in Typescript:

```
type PageType = "test";

interface Message {
    text: string;
    type: "received" | "sent" | "system";
    messageType: "image" | "text";
    timestamp?: Date;
    imageSrc?: string;
};

interface UserInfo {
    name?: string;
    gender: "male" | "female";
    username?: string;
    city?: string;
    postalCode?: string;
    country?: "DE" | "CH" | "AT";
    occupation?: string;
    education?: string;
    birthDate: {
        age?: number;
        date?: Date;
    };
    hobbies?: string;
    music?: string;
    movies?: string;
    books?: string;
    sports?: string;
    activities?: string;
    relationshipStatus?: string;
    lookingFor?: string;
    hasProfilePic?: boolean;
    hasPictures?: boolean;
    profileText?: string;
    rawText?: string;
    physicalDescription?: string;
    eyeColor?: string;
    height?: string;
    bodyType?: string;
    smoking?: string;
    hasTattoo?: boolean;
    housing?: string;
    hasCar?: boolean;
    personality?: string;
    hairColor?: string;
    children?: string;
    speaks?: string;
    zodiac?: string;
}

interface UserNotes {
    rawText: string;
}

interface Update {
    date?: Date;
    description: string;
}

interface SiteInfos {
    origin: PageType;
    messages: Message[];
    accountId?: string;
    html: string;
    metaData: {
        minLength: number;
        moderatorInfo: UserInfo;
        customerInfo: UserInfo;
        moderatorId?: string;
        customerId?: string;
        moderatorNotes?: UserNotes;
        customerNotes?: UserNotes;
        moderatorUpdates?: Update[];
        customerUpdates?: Update[];
        sessionStart?: Date;
        ins?: number;
        outs?: number;
        type?: string;
        alertBoxMessages?: string[];
        chatId?: string;
        customerProfilePic?: string; // as a url
        moderatorProfilePic?: string; // as a url
        html?: string;
        metaData?: any;
    }
};

```

# SiteInfos

It is very important for us to have the correct genders in the SiteInfos of the moderator and the customer.
You can of course send us the profile pic urls, so that we have context about the looks of the customer and the moderator.

# Image sending

If you want us to send images, you can provide us with your gallery of images.
We analyze them, keep them up to date on our backend and respond with matching image urls and messages that you can send to the customer.

# Notes vs Infos:

We differentiate between notes and infos. Infos are the static information about the fake and the customer.
When the AI or a moderator finds out new information, we return it in the notes in following schema:

-   Name: value
-   Beruf: value
    .
    .
    .
-   {key_n}: {value_n}

We can change the structure of the summary based on your preferences too. But this seems to work pretty good.

# Messages:

Please send the messages to the backend in an order that the last message is at the end of the list.
Messages can of course include image urls or base64 encoded imageurls. We prefer direct urls, because otherwise the file size may get too large.

# Cors

If you want to connect from a website, we need to have the url to enable cors.
