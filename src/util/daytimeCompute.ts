import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);

export function currentDayTime(): {
    day: string;
    date: string;
    promptDay: string;
    time: string;
    dayTime: string;
    nightString?: string;
} {
    let now = dayjs().utc().add(2, "hours");

    let day = now.locale("de").format("dddd");
    day = weekdayToGerman(day);
    const time = now.format("HH:mm");

    const date = now.locale("de").format("D.MMMM YYYY");

    // Bestimme die Tageszeit basierend auf der Uhrzeit
    const hour = parseInt(now.format("HH"), 10);
    const minute = parseInt(now.format("mm"), 10);
    let dayTime;
    let nightString;

    // Check if it's night time (22:00 to 04:30)
    const isNightTime = hour >= 22 || hour < 4 || (hour === 4 && minute <= 30);

    if (isNightTime) {
        dayTime = "Nacht";

        // Generate night string based on time
        if (hour >= 23) {
            // 22:00-23:59: "Night from current day to next day"
            const nextDay = now.add(1, "day").locale("de").format("dddd");
            const nextDayGerman = weekdayToGerman(nextDay);
            nightString = `(Es ist die Nacht von ${day} auf ${nextDayGerman})`;
        } else {
            // 00:00-04:30: "Night from previous day to current day"
            const previousDay = now
                .subtract(1, "day")
                .locale("de")
                .format("dddd");
            const previousDayGerman = weekdayToGerman(previousDay);
            nightString = `(Es ist die Nacht von ${previousDayGerman} auf ${day})`;
        }
    } else if (hour >= 4 && hour < 11) {
        dayTime = "Morgen";
    } else if (hour >= 11 && hour < 15) {
        dayTime = "Mittag";
    } else if (hour >= 15 && hour < 17) {
        dayTime = "Nachmittag";
    } else if (hour >= 17 && hour <= 22) {
        dayTime = "Abend";
    }

    const result: any = {
        day,
        date,
        promptDay: day,
        time: time,
        dayTime: dayTime,
    };

    if (nightString) {
        result.day = day + " " + nightString;
        result.promptDay = day;
    }

    return result;
}

function weekdayToGerman(weekday: string) {
    const localeMap = {
        Monday: "Montag",
        Tuesday: "Dienstag",
        Wednesday: "Mittwoch",
        Thursday: "Donnerstag",
        Friday: "Freitag",
        Saturday: "Samstag",
        Sunday: "Sonntag - Es haben alle Geschäfte zu!",
    };

    // @ts-ignore
    if (!localeMap[weekday]) {
        return weekday;
    }

    // @ts-ignore
    return localeMap[weekday];
}

console.log(currentDayTime());
