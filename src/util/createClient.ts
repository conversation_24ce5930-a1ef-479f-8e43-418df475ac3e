import {
    SupabaseClient,
    createClient as supabaseCreateClient,
} from "@supabase/supabase-js";
import dotenv from "dotenv";

dotenv.config();

// Create a singleton instance of the Supabase client
const supabaseAdmin = supabaseCreateClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!
);

export function supabaseAdminClient() {
    return supabaseAdmin;
}

export default supabaseAdminClient;
