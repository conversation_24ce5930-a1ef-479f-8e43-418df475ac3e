import levenshtein from "fast-levenshtein";
import { Message } from "../models/SiteInfos";

export default function checkDuplicateMessages(
    messages: Message[],
    gptMessage: string
): boolean {
    // Check for exact duplicates including the new message
    if (messages.some((m) => m.text === gptMessage)) {
        return true;
    }

    // Check if any sentence from gptMessage exists in previous messages
    // Split gptMessage into sentences based on common terminators (.!?)
    const aiSentences: string[] = gptMessage.match(/[^.!?]+[.!?]+/g) ?? [];

    // If regex didn't find sentences (e.g., no punctuation), treat the whole message
    // as one sentence, provided it's not just whitespace.
    if (aiSentences.length === 0 && gptMessage.trim().length > 0) {
        aiSentences.push(gptMessage);
    }

    for (const sentence of aiSentences) {
        const trimmedSentence = sentence.trim();
        // Skip empty strings that might result from splitting
        if (trimmedSentence.length === 0) continue;

        // Check if the trimmed sentence is included in any previous message's text
        if (messages.some((m) => m.text && m.text.includes(trimmedSentence))) {
            // Found a duplicate sentence
            return true;
        }
    }

    // Check for any message that is 90% or more similar to gptMessage
    return messages.some((m) => {
        if (m.text == undefined) {
            console.warn(
                "Message text is undefined in checkDuplicateMessages, messages: ",
                messages,
                "gptMessage: ",
                gptMessage
            );
            return false;
        }
        const lenGpt = gptMessage.length;
        const lenMsg = m.text.length;
        const maxLen = Math.max(lenGpt, lenMsg);
        if (maxLen === 0) return false; // Both strings are empty, not considered a duplicate

        const distance = levenshtein.get(gptMessage, m.text);
        const similarity = (maxLen - distance) / maxLen;

        return similarity >= 0.5;
    });
}
