import OpenAI from "openai";
import { AIAnalysisService } from "../services/inference/gemini";
import { cleanupOutput } from "./cleanup";
import { SiteInfos } from "../models/SiteInfos";

// Define the emoji regex outside the function so it's only compiled once
// Matches common emojis and their variations globally
const emojiRegex = /\p{Emoji_Presentation}|\p{Emoji}\uFE0F/gu;
// Define a regex to specifically check if a string *ends* with an emoji character
const endsWithEmojiRegex = /(?:\p{Emoji_Presentation}|\p{Emoji}\uFE0F)$/u;

export async function appendEmoji(
    text: string,
    siteInfos: SiteInfos
): Promise<string> {
    // Trim trailing whitespace to correctly check the last character
    const trimmedText = text.trimEnd();

    // Check if the trimmed text is not empty and ends with an emoji
    if (trimmedText.length > 0 && endsWithEmojiRegex.test(trimmedText)) {
        // If it already ends with an emoji, return the original text without calling AI
        return text;
    }

    // --- If it doesn't end with an emoji, proceed with AI ---
    const prompt = `
    Process the message provided below. Your sole action should be to add a single emoji from this list: 😊❤️💪🏼😋😉😍🤤☺️😚😃🫣🥹😎😩🥰🙃😇😝♥️🔥🍆🍑💦 to the very end of the message. Ensure the original message remains completely unchanged.
    `;

    let openaiMessages: OpenAI.ChatCompletionMessageParam[] = [
        {
            role: "user",
            content: text,
        },
    ];

    const inferenceRes = await AIAnalysisService.generateTextFromPrompt(
        prompt,
        openaiMessages,
        0,
        0,
        "gemini-2.0-flash-lite-001"
    );

    if (inferenceRes.success) {
        // Get the potentially modified text from the AI
        let generatedTextWithPotentialEmoji: string | null =
            inferenceRes.data!.generatedText;

        generatedTextWithPotentialEmoji = cleanupOutput(
            generatedTextWithPotentialEmoji!,
            siteInfos
        );

        if (!generatedTextWithPotentialEmoji) {
            return text;
        }

        // Reset the global regex lastIndex before using replace
        emojiRegex.lastIndex = 0;
        // Remove emojis from the *generated* text
        const generatedTextWithoutEmoji = generatedTextWithPotentialEmoji
            .replace(emojiRegex, "")
            .trim();

        // Reset the global regex lastIndex again
        emojiRegex.lastIndex = 0;
        // Remove emojis from the *original* text
        const originalTextWithoutEmoji = text.replace(emojiRegex, "").trim();

        // Compare the emoji-removed original text with the emoji-removed generated text
        if (originalTextWithoutEmoji === generatedTextWithoutEmoji) {
            // If the core text matches, it means the AI likely just added an emoji
            // (or modified existing emojis without changing the text content)
            // Return the AI's full output
            return generatedTextWithPotentialEmoji;
        } else {
            // If the core text was changed by the AI, log a warning and return the original unchanged text
            // (Adding the console.warn back as it's useful for debugging)
            console.warn(
                "AI modified the original text content. Reverting to original text. Original:",
                text,
                "Generated:",
                generatedTextWithPotentialEmoji
            );
            return text;
        }
    }

    // If the inference was not successful, return the original text
    return text;
}
