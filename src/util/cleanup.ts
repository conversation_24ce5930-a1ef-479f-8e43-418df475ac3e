import { PageType, SiteInfos } from "../models/SiteInfos";

// sometimes ai response texts have weird artifacts. we need to clean them
export function cleanupOutput(text: string | null, siteInfos: SiteInfos) {
    if (!text) return null;

    const origin = siteInfos.origin;

    // Extract content between <Nachricht>, <content>, <message> tags if present
    const extracted = extractMessageTagContent(text);
    if (extracted) {
        text = extracted;
    }

    let cleanedText = cleanupArtefacts(text);

    if (cleanedText.startsWith(",")) {
        cleanedText = cleanedText.substring(1).trim();
    }

    if (cleanedText.includes(":")) {
        const parts = cleanedText.split(/[:|::]/);
        // Only remove the colon if there's exactly one word before it
        if (parts[0].trim().split(/\s+/).length === 1) {
            cleanedText = parts.slice(1).join(":").trim();
        } else {
            // Keep the colon if there's more than one word before it
            cleanedText = parts.join(":").trim();
        }
    }

    // Make the first letter of the first word uppercase
    cleanedText = cleanedText.replace(/^\s*\w/, (match) => match.toUpperCase());

    // Remove " Bild" prefix if present
    cleanedText = cleanedText.replace(/^\s*Bild\s*/, "");

    // Handle "Hach, " prefix
    cleanedText = cleanedText.replace(/^Hach,\s*/, () => {
        const options = ["Ach, ", "Oh, ", "Hach, ", ""];
        return options[Math.floor(Math.random() * options.length)];
    });

    // Handle "Ey, " prefix - remove "Ey, " from the beginning
    cleanedText = cleanedText.replace(/^Ey,\s*/i, "");

    if (siteInfos.metaData.customerInfo.gender == "female") {
        cleanedText = cleanedText.replaceAll("mein Lieber", "meine Liebe");
        cleanedText = cleanedText.replaceAll("Mein Lieber", "Meine Liebe");
    } else if (siteInfos.metaData.customerInfo.gender == "male") {
        cleanedText = cleanedText.replaceAll("meine Liebe", "mein Lieber");
        cleanedText = cleanedText.replaceAll("Meine Liebe", "Mein Lieber");
    }

    if (
        siteInfos.metaData.customerInfo.country == "CH" ||
        origin == "b3" ||
        origin == "gold" ||
        origin == "lacarna" ||
        origin == "myloves" ||
        origin == "onlydates69" ||
        origin == "xloves" ||
        origin == "whatsmeet"
    ) {
        cleanedText = cleanedText.replaceAll("ß", "ss");
    }

    if (origin == "fpc" || origin == "teddy") {
        // Remove all emojis using a regex pattern that matches emoji unicode ranges
        cleanedText = cleanedText.replace(
            /[\p{Emoji_Presentation}\p{Extended_Pictographic}]/gu,
            ""
        );
    }

    const removeEmojis = [
        "🫥",
        "😶‍🌫️",
        "🥶",
        "👹",
        "👺",
        "🤡",
        "💩",
        "👻",
        "💀",
        "☠️",
        "👽",
        "👾",
        "🤖",
        "🌺",
        "🌸",
        "🌼",
        "🌻",
        "🌞",
        "🌝",
        "🌛",
        "🌜",
        "🌚",
        "🌕",
        "🌖",
        "🌗",
        "🌘",
        "🌑",
        "🌒",
        "🌓",
        "🌔",
        "🌙",
        "🪐",
        "💫",
        "⭐️",
        "🌟",
        "✨",
        "⚡️",
        "💥",
        "☄️",
        "🌈",
        "☀️",
        "🌤️",
        "⛅️",
        "🌥️",
        "☁️",
        "🌦️",
        "🌧️",
        "💧",
        "💨",
        "🌬️",
        "⛄️",
        "☃️",
        "❄️",
        "🌨️",
        "🌩️",
        "⛈️",
        "💦",
        "🫧",
        "☔️",
        "☂️",
        "🌊",
        "🌫️",
        "💭",
        "💬",
        "🎿",
    ];

    removeEmojis.forEach((emoji) => {
        cleanedText = cleanedText.replace(emoji, "");
    });

    // Ensure the first letter is capitalized, even after all other replacements
    cleanedText = cleanedText.replace(/^[a-z]/, (letter) =>
        letter.toUpperCase()
    );

    cleanedText = cleanedText.replace(/\s-\s*(\w)/g, (match, nextChar) => {
        return `. ${nextChar.toUpperCase()}`;
    });

    // Also handle en dashes the same way
    cleanedText = cleanedText.replace(/\s–\s*(\w)/g, (match, nextChar) => {
        return `. ${nextChar.toUpperCase()}`;
    });

    // Also handle em dashes the same way
    cleanedText = cleanedText.replace(/\s—\s*(\w)/g, (match, nextChar) => {
        return `. ${nextChar.toUpperCase()}`;
    });

    cleanedText = cleanedText.replaceAll("–", "-").replaceAll("—", "-");
    cleanedText = cleanedText.replaceAll("-", " ");

    cleanedText = cleanedText.replaceAll("...", ".").replaceAll(" .", ".");

    return cleanedText.trim();
}

export function cleanupOutputSummary(text: string) {
    return text
        .replaceAll("{", "")
        .replaceAll("}", "")
        .replaceAll("[", "")
        .replaceAll("]", "")
        .replaceAll("(", "")
        .replaceAll(")", "")
        .replaceAll("#", "")
        .replaceAll("*", "")
        .replaceAll("<|im_end|>", "")
        .replaceAll("<|im_start|>", "")
        .replaceAll("N/A", "NULL")
        .replaceAll("Unbekannt", "NULL")
        .replaceAll("Nicht erwähnt", "NULL");
}

/**
 * Extracts the content between tags like <Nachricht>...</Nachricht>, <content>...</content>, etc.
 * Supports: nachricht, content, message (case-insensitive).
 * Returns the matched content, or the original text if no match is found.
 */
function extractMessageTagContent(text: string): string {
    const match = text.match(/<(nachricht|content|message)>(.*?)<\/\1>/i);
    return match ? match[2].trim() : text;
}

export function cleanupArtefacts(text: string) {
    return (
        text
            .replaceAll(`"`, "")
            .replaceAll("{", "")
            .replaceAll("}", "")
            .replaceAll("[", "")
            .replaceAll("]", "")
            .replaceAll("(", "")
            // Preserve emoticons like ;) :( :D
            .replace(/(?<![;:])\)/g, "")
            .replaceAll("#", "")
            .replaceAll("*", "")
            .replaceAll("\n", "")
            .replaceAll("\r", "")
            .replaceAll("\t", "")
            .replaceAll(`"`, "")
            .replaceAll("`", "")
            .replaceAll("'", "")
            .replaceAll("„", "")
            .replaceAll("“", "")
            .replaceAll("”", "")
            .replaceAll("’", "")
            .replaceAll("„", "")
            .replaceAll("<|im_end|>", "")
            .replaceAll("<|im_start|>", "")
            .replaceAll("dannach", "danach")
            .replaceAll("hälst ", "hältst ")
            .replaceAll("zwinkerndes smiley", ";)")
            .replaceAll("(ausgeblendet)", "Hat Telefonnummer gesendet")
            .replaceAll("Assistant::", "")
            .replaceAll("assistant::", "")
            .replaceAll("Assistent::", "")
            .replaceAll("assistent::", "")
            .replaceAll("Assistant:", "")
            .replaceAll("assistant:", "")
            .replaceAll("Assistent:", "")
            .replaceAll("assistent:", "")
            .replaceAll("model::", "")
            .replaceAll("model:", "")
            .replaceAll("Model::", "")
            .replaceAll("Model:", "")
            .replaceAll("user::", "")
            .replaceAll("user:", "")
            .replaceAll("User::", "")
            .replaceAll("User:", "")
            .replaceAll("Output:", "")
            .replaceAll("output:", "")
            .replaceAll("Output::", "")
            .replaceAll("output::", "")
            .replaceAll("Gesendet", "")
            .replaceAll("<Nachricht>", "")
            .replaceAll("</Nachricht>", "")
            .replaceAll("Bild gesendet", "")
            .replaceAll("Erotisches Bild wird gesendet", "")
            .replaceAll(":", ".")
    );
}
