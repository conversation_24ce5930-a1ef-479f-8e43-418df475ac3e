import * as express from "express";
import { getUserByToken } from "../services/supabase/user";
import { StatusCodes } from "http-status-codes";
import supabaseAdminClient from "../util/createClient";
import jwt from "jsonwebtoken";

declare global {
    namespace Express {
        interface Request {
            user?: any;
            token?: string;
        }
    }
}

const authMiddleware = async (
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
) => {
    try {
        // Check for API key first
        const apiKey = req.headers["x-api-key"];
        const apiKeys = process.env.API_KEYS?.split(",");
        if (apiKeys?.includes(apiKey as string)) {
            // If API key is valid, proceed without user info
            next();
            return;
        }

        // If no valid API key, proceed with JWT authentication
        const authHeader = req.headers["authorization"];
        const token = authHeader && authHeader.split(" ")[1]; // Bearer TOKEN

        if (!token) {
            return res.status(StatusCodes.UNAUTHORIZED).json({
                error: "Access token is required",
            });
        }

        try {
            // Verify JWT locally first
            // Note: JWT_SECRET should be set to match Supabase's JWT secret
            const JWT_SECRET = process.env.JWT_SECRET!;
            const decoded = jwt.verify(token, JWT_SECRET);

            // Basic user info from JWT payload
            req.user = {
                id: (decoded as any).sub,
                email: (decoded as any).email,
                aud: (decoded as any).aud,
            };

            req.token = token;

            next();
        } catch (jwtError) {
            // Only call Supabase if local verification fails
            // This could be due to token expiration or signature issues
            const { data, error } = await supabaseAdminClient().auth.getUser(
                token
            );

            if (error || !data.user) {
                return res.status(StatusCodes.UNAUTHORIZED).json({
                    error: "Invalid or expired token",
                });
            }

            // Set user and token in request object
            req.user = data.user;
            req.token = token;

            next();
        }
    } catch (error) {
        console.error("Auth middleware error:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            error: "Authentication failed",
        });
    }
};

export const apiKeyMiddleware = (
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
) => {
    const apiKey = req.headers["x-api-key"];
    const apiKeys = process.env.API_KEYS?.split(",");
    if (!apiKeys?.includes(apiKey as string)) {
        return res.status(StatusCodes.UNAUTHORIZED).json({
            error: "Invalid API key",
        });
    }
    next();
};

export default authMiddleware;
