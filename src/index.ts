import express, { Express, Request, Response } from "express";
import helmet from "helmet";
import dotenv from "dotenv";
import rateLimiter from "express-rate-limit";
import translateRouter from "./routes/translate";
const { xss } = require("express-xss-sanitizer");
import cors from "cors";
import transcribeRouter from "./routes/transcribe";

import bodyParser from "body-parser";
import glossaryRouter from "./routes/glossary";
import authRouter from "./routes/auth";
import authMiddleware from "./middleware/auth";
import promptConfigRouter from "./routes/promptConfig";
import chatCompletionRouter from "./routes/chatCompletion";
import { moderateNewFinetuningMessages } from "./cronjobs/moderation";
import macRouter, {
    requestRestart,
    requestRestartSplashtop,
} from "./routes/mac";
import TelegramBot from "node-telegram-bot-api";
import { getMacDB } from "./services/supabase/macs";
import { initializeTelegramBot } from "./services/telegram/bot";
import statusRouter from "./routes/status";
import correctOutputRouter from "./routes/correctOutputRouter";
import translateWithAIRouter from "./routes/translateWithAI";
import rewriteEmailRouter from "./routes/rewriteEmail";
import chatUsersRouter from "./routes/chatUsers";

/// IMPORTANT:
/**
 * The server does not work locally when opening the frontend from incogniton
 */

dotenv.config();
const app: Express = express();
const port = process.env.PORT || 3000;

const corsOptions = {
    origin: [
        "http://localhost:5500",
        "http://localhost:5501",
        "http://localhost:3002",
        "http://127.0.0.1:5500",
        "http://127.0.0.1:5501",
        "https://mods.gold-chat.net",
        "https://mods.mltestapp.com",
        "https://mods.platin-chat.com",
        "https://mods.diamondchat.net",
        "https://mods.onlydates69.com",
        "https://chathomebase.com",
        "https://agents.moderationinterface.com",
        "https://blr.chat",
        "https://www.blr.chat",
        "https://zumblenny.com",
        "https://mod.wi2020fu.de",
        "https://app.fpc.tools",
        "https://typen.app",
        "https://teddy-mod.de",
        "https://teddy-sys-mod.de",
        "https://moderation.dreamteam-media-ug.de",
        "https://kizzle.net",
        "https://flirtrakete.net",
        "https://bettdeckengefluester.de",
        "https://www.international-dating.net",
        "https://chat.route66.dev",
        "https://workingspace.center",
        "https://mod.love-room.de",
        "https://chat.pankek.io",
        "https://kleineflirts-cherry.mod-panel.de",
        "https://livecreator.com",
        "https://cherry.mod-panel.app",
        "https://mod20240923.verbotenfrech.de",
        "https://hellovy.mod-panel.de",
        "https://hellovy-cherry.mod-panel.de",
        "https://demo-sexytalk.vercel.app",
        "https://torchwood.app",
        "https://single-jungle.net",
        "https://lvchat.de",
        "https://paario.de",
        "https://xkuss.com",
        "https://mod.justlo.de",
        "https://chat-maestro-ecru.vercel.app",
    ],
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "x-api-key"],
};

initializeTelegramBot();

app.use(cors(corsOptions));

// Enable pre-flight requests for all routes
app.options("*", cors(corsOptions));

app.set("trust proxy", true);

// Increase the limit to, for example, 50mb
app.use(bodyParser.json({ limit: "25mb" }));
app.use(bodyParser.urlencoded({ limit: "25mb", extended: true }));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(
    helmet({
        crossOriginResourcePolicy: false,
    })
);
app.use(xss());

app.use(rateLimiter({ windowMs: 1000, max: 6000 })); // max 60 requests per second

app.get("/", (req: Request, res: Response) => {
    res.send("Express + TypeScript Server");
});

app.use("/translate", authMiddleware, translateRouter);
app.use("/translate-with-ai", authMiddleware, translateWithAIRouter);
app.use("/transcribe", authMiddleware, transcribeRouter);
app.use("/glossary", authMiddleware, glossaryRouter);
app.use("/chatcompletion", authMiddleware, chatCompletionRouter);
app.use("/promptconfig", authMiddleware, promptConfigRouter);
app.use("/auth", authRouter);
app.use("/macs", authMiddleware, macRouter);
app.use("/rewrite-email", authMiddleware, rewriteEmailRouter);
app.use("/status", statusRouter);
app.use("/correct_output", authMiddleware, correctOutputRouter);
app.use("/chat-users", authMiddleware, chatUsersRouter);

app.listen(port, () => {});

/// MARK: - Cron jobs
// if (process.env.ENVIRONMENT === "production") {
//     cron.schedule("0,10,20,30,40,50 * * * *", moderateNewFinetuningMessages);
// }

// moderateNewFinetuningMessages();
