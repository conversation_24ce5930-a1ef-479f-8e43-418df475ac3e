import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import {
    getPromptConfigDB,
    getPromptDB,
} from "../services/supabase/promptConfig";

export async function getPromptConfig(req: Request, res: Response) {
    const { id } = req.query;

    const promptConfig = await getPromptConfigDB({ id: Number(id) });

    const promptsForPromptConfig = await Promise.all(
        Object.values(promptConfig?.config).map((promptId) => {
            return getPromptDB(promptId as number);
        })
    );

    if (promptConfig) {
        return res
            .status(StatusCodes.OK)
            .json({ promptConfig, prompts: promptsForPromptConfig });
    }
    return res.status(StatusCodes.NOT_FOUND).send();
}

export async function getPrompt(req: Request, res: Response) {
    const { id } = req.query;

    const prompt = await getPromptDB(Number(id));

    if (prompt) {
        return res.status(StatusCodes.OK).json(prompt);
    }
    return res.status(StatusCodes.NOT_FOUND).send();
}
