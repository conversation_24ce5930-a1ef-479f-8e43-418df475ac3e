import {
    SignInWithPasswordCredentials,
    createClient,
} from "@supabase/supabase-js";
import { Request, Response } from "express";
import supabaseAdminClient from "../util/createClient";
import { StatusCodes } from "http-status-codes";

export async function login(req: Request, res: Response) {
    // Get credentials from either body or query parameters
    const username = req.body.email || req.query.email;
    const password = req.body.password || req.query.password;

    if (!username || !password) {
        return res.status(StatusCodes.BAD_REQUEST).json({
            error: "Username and password are required",
        });
    }

    const credentials: SignInWithPasswordCredentials = {
        email: username, // Using username as email for Supabase compatibility
        password,
    };

    const { data, error } = await supabaseAdminClient().auth.signInWithPassword(
        credentials
    );

    if (error) {
        console.error("Login error:", error);
        return res
            .status(error.status || StatusCodes.UNAUTHORIZED)
            .json({ error: error.message });
    }

    return res.status(StatusCodes.OK).json({
        access_token: data.session.access_token,
        refresh_token: data.session.refresh_token,
        expires_at: data.session.expires_at,
        user: data.user,
    });
}

export async function register(req: Request, res: Response) {
    const { username, password } = req.body;

    if (!username || !password) {
        return res.status(StatusCodes.BAD_REQUEST).json({
            error: "Username and password are required",
        });
    }

    const { data, error } = await supabaseAdminClient().auth.signUp({
        email: username, // Using username as email for Supabase compatibility
        password,
    });

    if (error) {
        return res
            .status(error.status || StatusCodes.BAD_REQUEST)
            .json({ error: error.message });
    }

    return res.status(StatusCodes.CREATED).json({
        message: "User registered successfully",
        user: data.user,
    });
}

export async function refreshToken(req: Request, res: Response) {
    const { refresh_token } = req.body;

    if (!refresh_token) {
        return res.status(StatusCodes.BAD_REQUEST).json({
            error: "Refresh token is required",
        });
    }

    const { data, error } = await supabaseAdminClient().auth.refreshSession({
        refresh_token,
    });

    if (error) {
        return res
            .status(error.status || StatusCodes.UNAUTHORIZED)
            .json({ error: error.message });
    }
    if (!data.session) {
        return res.status(StatusCodes.UNAUTHORIZED).json({
            error: "Session is null",
        });
    }

    return res.status(StatusCodes.OK).json({
        access_token: data.session.access_token,
        refresh_token: data.session.refresh_token,
        expires_at: data.session.expires_at,
        user: data.user,
    });
}

export async function logout(req: Request, res: Response) {
    const { error } = await supabaseAdminClient().auth.signOut();

    if (error) {
        return res
            .status(StatusCodes.INTERNAL_SERVER_ERROR)
            .json({ error: error.message });
    }

    return res.status(StatusCodes.OK).json({
        message: "Logged out successfully",
    });
}
