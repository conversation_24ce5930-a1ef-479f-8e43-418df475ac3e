import { Request, Response } from "express";
import { getPromptDB } from "../services/supabase/promptConfig";
import { AIAnalysisService } from "../services/inference/gemini";
import { createAnalyticsItem } from "../services/supabase/chatCompleteAnalyticsService";

export const translateTextWithAI = async (text: string) => {
    const prompt = await getPromptDB(106);

    if (!prompt) {
        throw new Error("Prompt not found");
    }

    console.log("prompt", prompt);

    const response = await AIAnalysisService.generateTextFromPrompt(
        prompt.prompt,
        [{ role: "user", content: text }],
        prompt.ai_model_config.temperature
            ? (prompt.ai_model_config.temperature as number)
            : 0.7,
        undefined,
        prompt.ai_model_config.model
    );

    console.log("response", response);

    await createAnalyticsItem({
        chat_complete_prompt_id: 106,
        prev_messages: [],
        origin_website: "chattranslate.cloud",
        language: "de",
        // @ts-ignore
        ai_generated_message: response.data.generatedText,
        ai_model: prompt.ai_model_config.model,
        metadata: {
            temperature: prompt.ai_model_config.temperature || 0.7,
            top_p: prompt.ai_model_config.top_p || 0.9,
            model: prompt.ai_model_config.model,
        },
    });

    return response;
};

const translateWithAI = async (req: Request, res: Response) => {
    const { text } = req.body;

    try {
        const response = await translateTextWithAI(text);
        res.json(response);
    } catch (error) {
        console.error("Error in translateWithAI:", error);
        res.status(500).json({ error: "Translation failed" });
    }
};

export default translateWithAI;
