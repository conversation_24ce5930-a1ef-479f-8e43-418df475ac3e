import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";

import {
    ChatCompletionParams,
    generateChatCompletion,
} from "../services/inference/generateChatCompletion";
import { handleErrors } from "../services/chatMaestro/sendToMaestro";

/**
 * Custom error class for timeout errors
 */
class TimeoutError extends Error {
    constructor(message: string = 'Operation timed out') {
        super(message);
        this.name = 'TimeoutError';
    }
}

export async function createInferenceChatCompletionController(
    req: Request,
    res: Response
) {
    const params: ChatCompletionParams = req.body;
    const { user } = req;

    // Track the execution start time for proper expiration calculation
    const executionStartTime = new Date();

    // Add the start time to params so generateChatCompletion can use it
    const paramsWithStartTime = {
        ...params,
        startedAt: executionStartTime
    };

    try {
        // Create a timeout promise that rejects after 2.5 minutes
        const timeoutPromise = new Promise<never>((_, reject) => {
            setTimeout(() => {
                reject(new TimeoutError('Chat completion timed out after 2.5 minutes'));
            }, 150000); // 2.5 minutes in milliseconds
        });

        // Race between the actual operation and the timeout
        const completionRes = await Promise.race([
            generateChatCompletion(paramsWithStartTime, user),
            timeoutPromise
        ]);

        return res.status(StatusCodes.OK).json(completionRes);
    } catch (error) {
        if (error instanceof TimeoutError) {
            console.error('Chat completion timed out, distributing to chat maestro:', error.message);

            try {
                // Distribute the task to chat maestro when timeout occurs
                const maestroResult = await handleErrors({
                    resText: "",
                    alert: "TIMEOUT: Chat completion timed out after 2.5 minutes",
                    prompt: { name: params.siteInfos.metaData.type ?? "default" },
                    siteInfos: params.siteInfos,
                    extractedMetadata: {},
                    startedAt: executionStartTime, // Use the original execution start time
                    summary: {
                        assistant: {},
                        user: {}
                    },
                    disableAutoSend: true
                });

                return res.status(StatusCodes.OK).json(maestroResult);
            } catch (maestroError) {
                console.error('Error distributing to chat maestro:', maestroError);
                return res.status(StatusCodes.REQUEST_TIMEOUT).json({
                    error: 'Request timed out',
                    message: 'The chat completion operation took too long to complete and fallback failed'
                });
            }
        }

        // Handle other errors
        console.error('Error in chat completion:', error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            error: 'Internal server error',
            message: 'An unexpected error occurred'
        });
    }
}
