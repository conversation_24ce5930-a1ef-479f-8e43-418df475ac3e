import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { getChatUsersByOrigin } from "../services/supabase/chatUsers";

export async function getChatUsersByOriginController(
    req: Request,
    res: Response
) {
    try {
        const origin = req.query.origin as string;

        if (!origin) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                error: "Origin parameter is required",
            });
        }

        const users = await getChatUsersByOrigin(origin);

        return res.status(StatusCodes.OK).json({
            users: users || [],
            success: true,
        });
    } catch (error) {
        console.error("Error fetching users by origin:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            error: "Failed to fetch users",
        });
    }
}
