import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { translateDeepl } from "../services/deepl_translate";
import * as deepl from "deepl-node";
import AgentAnalyticsItem from "../models/AgentAnalytics";
import { saveAgentAnalyticsItem } from "../services/supabase/agentAnalyticsService";
import { getRoleByToken } from "../services/supabase/user";
import { getGlossaryByAgencyAndLanguage } from "../services/supabase/glossary";
import { translateGPT } from "../services/gpt_translate";
import { DeeplSupportedLanguage } from "../models/DeeplSupportedLanguages";

function checkForbiddenWords(text: string): string[] {
    const forbiddenWords = ["liebe"];
    return forbiddenWords.filter((word) => text.includes(word));
}

const translate = async (req: Request, res: Response) => {
    const user = req.user;
    const role = await getRoleByToken(req.token);

    const { source_lang, target_lang } = req.query as {
        source_lang: DeeplSupportedLanguage;
        target_lang: DeeplSupportedLanguage;
    };

    const { text } = req.body;

    if (!text || text == "") {
        res.status(StatusCodes.BAD_REQUEST).send({
            message: "No text to translate",
        });
        return;
    }

    if (!role) {
        res.status(StatusCodes.UNAUTHORIZED).send({
            message: "Role for agent not found",
        });
        return;
    }

    try {
        const translatedText = await translateAndVerify(
            text,
            source_lang,
            target_lang,
            role.agency_id
        );

        const analyticsItem: AgentAnalyticsItem = {
            agent_id: user.id as string,
            origin_text: text,
            translated_text: translatedText,
            ai_enhanced_text: translatedText,
            target_language: target_lang,
            source_language: source_lang,
        };

        await saveAgentAnalyticsItem(analyticsItem);

        return res
            .status(StatusCodes.OK)
            .send({ translatedText: translatedText });
    } catch (error) {
        console.error("Translation error:", error);
        return res
            .status(StatusCodes.INTERNAL_SERVER_ERROR)
            .send({ message: "Translation failed" });
    }
};

const translateAndVerify = async (
    text: string,
    source_lang: DeeplSupportedLanguage,
    target_lang: DeeplSupportedLanguage,
    agency_id: string
): Promise<string> => {
    const glossaryId = await getGlossaryByAgencyAndLanguage(
        agency_id,
        target_lang,
        source_lang
    );
    const deeplAuthKey = process.env.DEEPL_API_KEY!;
    const translator = new deepl.Translator(deeplAuthKey);

    let entries: Record<string, string> = {};
    if (glossaryId) {
        entries = (await translator.getGlossaryEntries(glossaryId)).entries();
    }

    let translatedText = await translateGPT(
        text,
        source_lang,
        target_lang,
        entries
    );

    if (translatedText == null) {
        translatedText = await translateDeepl(
            text,
            source_lang
                ? (source_lang.toLowerCase() as deepl.SourceLanguageCode)
                : "es",
            target_lang
                ? (target_lang.toLowerCase() as deepl.TargetLanguageCode)
                : "de",
            glossaryId
        );
    }

    const forbiddenWords = checkForbiddenWords(translatedText);
    if (forbiddenWords.length > 0) {
        return "forbidden words detected. " + forbiddenWords.join(", ");
    }

    return translatedText;
};

export default translate;
