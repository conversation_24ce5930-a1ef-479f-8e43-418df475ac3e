import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { PageType } from "../models/SiteInfos";
import { getPromptDB } from "../services/supabase/promptConfig";
import { mergePromptWithObject } from "../services/inference/mergePrompt";
import {
    AIAnalysisService,
    calculateCosts,
} from "../services/inference/gemini";
import OpenAI from "openai";
import { createAnalyticsItem } from "../services/supabase/chatCompleteAnalyticsService";
import { getRewrittenAttributes } from "../services/rewrite/rewriteAttributes";
import { cleanupArtefacts } from "../util/cleanup";

interface RewriteEmailMetadata {
    age: number;
    gender: "male" | "female";
}

export async function rewriteEmailController(req: Request, res: Response) {
    const {
        subject,
        body,
        metadata,
        origin,
    }: {
        subject: string; // Clean text
        body: string; // Clean text
        metadata: RewriteEmailMetadata;
        origin: PageType;
    } = req.body;

    const rewriteAttributes = await getRewrittenAttributes(origin, {
        age: 20,
        gender: "male",
    });

    const rewriteConfig = {
        rewriteAge: 20,
        rewriteAdjectives: rewriteAttributes.join(", "),
    };

    // Get prompts for both operations
    const subjectPrompt = await getPromptDB(113); // Subject rewriting prompt
    const bodyPrompt = await getPromptDB(112); // Body rewriting prompt

    if (!subjectPrompt || !bodyPrompt) {
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            error: "Failed to get prompts from database",
        });
    }

    // 1. Rewrite the subject
    const mergedSubjectPrompt = mergePromptWithObject(
        subjectPrompt.prompt,
        rewriteConfig
    );
    const subjectMessages: OpenAI.ChatCompletionMessageParam[] = [
        {
            role: "system",
            content: mergedSubjectPrompt,
        },
        {
            role: "user",
            content: subject,
        },
    ];

    const subjectResponse = await AIAnalysisService.generateTextFromPrompt(
        mergedSubjectPrompt,
        subjectMessages,
        subjectPrompt.ai_model_config.temperature,
        subjectPrompt.ai_model_config.top_p,
        "gemini-2.5-flash",
        false
    );

    // 2. Rewrite the body text
    const mergedBodyPrompt = mergePromptWithObject(
        bodyPrompt.prompt,
        rewriteConfig
    );
    const bodyMessages: OpenAI.ChatCompletionMessageParam[] = [
        {
            role: "system",
            content: mergedBodyPrompt,
        },
        {
            role: "user",
            content: body,
        },
    ];

    const bodyResponse = await AIAnalysisService.generateTextFromPrompt(
        mergedBodyPrompt,
        bodyMessages,
        bodyPrompt.ai_model_config.temperature,
        bodyPrompt.ai_model_config.top_p,
        "gemini-2.5-flash",
        false
    );

    // Calculate total costs
    const subjectCost = calculateCosts(
        "gemini-2.5-flash",
        subjectResponse.data?.usage
    );
    const bodyCost = calculateCosts(
        "gemini-2.5-flash",
        bodyResponse.data?.usage
    );

    // Create analytics for subject rewriting
    await createAnalyticsItem({
        chat_complete_prompt_id: 113,
        prev_messages: subjectMessages,
        origin_website: origin,
        language: "de",
        ai_generated_message: subjectResponse?.data?.generatedText ?? "",
        ai_model: subjectPrompt.ai_model,
        metadata: {
            ...metadata,
            rewriteAttributes: rewriteAttributes,
            usage: subjectResponse?.data?.usage,
            cost: subjectCost,
            operation: "subject_rewrite",
        },
        corrected_output: subjectResponse?.data?.generatedText ?? "",
    });

    // Create analytics for body rewriting
    await createAnalyticsItem({
        chat_complete_prompt_id: 112,
        prev_messages: bodyMessages,
        origin_website: origin,
        language: "de",
        ai_generated_message: bodyResponse?.data?.generatedText ?? "",
        ai_model: bodyPrompt.ai_model,
        metadata: {
            ...metadata,
            rewriteAttributes: rewriteAttributes,
            usage: bodyResponse?.data?.usage,
            cost: bodyCost,
            operation: "body_rewrite",
        },
        corrected_output: bodyResponse?.data?.generatedText ?? "",
    });

    const response = {
        rewrittenSubject: cleanupArtefacts(
            subjectResponse.data?.generatedText ?? ""
        ),
        rewrittenBody: cleanupArtefacts(bodyResponse.data?.generatedText ?? ""),
        rewriteAttributes,
        cost: subjectCost + bodyCost,
    };

    console.log("response", response);

    return res.status(StatusCodes.OK).json(response);
}
