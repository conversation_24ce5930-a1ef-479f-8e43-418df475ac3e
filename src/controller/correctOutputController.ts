import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";

import supabaseAdminClient from "../util/createClient";

export async function correctOutputController(req: Request, res: Response) {
    const params: {
        correctedText?: string;
        analyticsItemId: number;
    } = req.body;

    const { user } = req;

    if (!user || !user.email) {
        return res.status(StatusCodes.UNAUTHORIZED).json({
            error: "User email is required for correction",
        });
    }

    if (!params.analyticsItemId) {
        return res.status(StatusCodes.BAD_REQUEST).json({
            error: "analytics item ID is required",
        });
    }

    const supabase = supabaseAdminClient();

    let updatedData: any = {
        updated_by: user.email,
    };

    if (params.correctedText) {
        updatedData.corrected_output = params.correctedText;
    }

    try {
        const { data, error } = await supabase
            .from("chat_complete_analytics")
            .update(updatedData)
            .eq("id", params.analyticsItemId);

        if (error) {
            console.error("Error updating corrected output:", error);
            return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
                error: "Failed to update corrected output",
            });
        }

        return res.status(StatusCodes.OK).json({
            success: true,
            message: "Corrected output saved successfully",
        });
    } catch (err) {
        console.error("Exception updating corrected output:", err);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            error: "An unexpected error occurred",
        });
    }
}
