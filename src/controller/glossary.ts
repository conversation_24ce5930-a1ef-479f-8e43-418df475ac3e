import * as deepl from "deepl-node";
import { Request, Response } from "express";
import { getAgencyForUser } from "../services/supabase/agency";
import {
    createGlossaryInDb,
    findGlossaryDb,
    getGlossaryDb,
} from "../services/supabase/glossary";
import Glossary from "../models/Glossary";
import { StatusCodes } from "http-status-codes";

// you can create 1000 glossaries per deepl account. Glossaries include a target language and an origin language
export async function createGlossary(req: Request, res: Response) {
    // create glossary only creates a glossary entry in the db because you can't create an empty one on deepl...
    const { target_lang, source_lang } = req.query as {
        target_lang: string;
        source_lang: string;
    };
    const { token } = req.headers as { token: string };

    // glossary name is "${agency_name}_${source_lang}_${target_lang}"
    const agency = await getAgencyForUser(token);

    // TODO: check if glossary for origin and target lang already exists for agency

    const existingGlossary = await findGlossaryDb(
        agency.id,
        source_lang,
        target_lang
    );

    if (existingGlossary) {
        return res
            .status(StatusCodes.FORBIDDEN)
            .send(
                "Glossary for agency with source language and target language already exists"
            );
    }

    const glossaryName = `${agency.name}_${source_lang}_${target_lang}`;

    const glossaryItem: Glossary = {
        name: glossaryName,
        source_lang: source_lang as string,
        target_lang: target_lang as string,
        agency_id: agency.id,
    };

    await createGlossaryInDb(glossaryItem);

    return res.status(StatusCodes.CREATED).send();
}

export async function addGlossaryEntry(req: Request, res: Response) {
    const { entries } = req.body;
    const { glossary_id } = req.query;

    // Clean the entries
    const cleanedEntries: { [key: string]: string } = {};
    Object.keys(entries).forEach((key) => {
        const value = entries[key];
        const cleanedKey = cleanString(key);
        const cleanedValue = cleanString(value);
        cleanedEntries[cleanedKey] = cleanedValue;
    });

    const newEntries: deepl.GlossaryEntries = new deepl.GlossaryEntries({
        entries: cleanedEntries, // { artist: "Maler", prize: "Gewinn" }
    });

    const deeplAuthKey = process.env.DEEPL_API_KEY!;
    const translator = new deepl.Translator(deeplAuthKey);

    const glossaries = await translator.listGlossaries();
    const glossary = glossaries.find(
        (glossary) => glossary.name == glossary_id
    );

    //  const oldEntries = await translator.getGlossaryEntries(glossary_id as string);

    if (glossary) {
        try {
            translator.createGlossary(
                glossary_id as string,
                glossary!.sourceLang,
                glossary!.targetLang,
                newEntries
            );
        } catch (error) {
            return res.status(StatusCodes.BAD_REQUEST).send(error);
        }
        await translator.deleteGlossary(glossary?.glossaryId as string);
    } else {
        const glossaryDb = await getGlossaryDb(glossary_id as string);

        translator.createGlossary(
            glossary_id as string,
            glossaryDb.source_lang,
            glossaryDb.target_lang,
            newEntries
        );
    }
    return res.status(StatusCodes.OK).send();
}

export async function getGlossary(req: Request, res: Response) {
    const deeplAuthKey = process.env.DEEPL_API_KEY!;
    const translator = new deepl.Translator(deeplAuthKey);
    const { glossary_id } = req.query as { glossary_id: string };

    const glossaries = await translator.listGlossaries();
    const glossary = glossaries.find(
        (glossary) => glossary.name == glossary_id
    );

    if (!glossary) {
        // check if glossary exists in db.
        const glossaryInDb = await getGlossaryDb(glossary_id);
        if (glossaryInDb) {
            return res.status(StatusCodes.OK).json({
                glossary: {
                    sourceLang: glossaryInDb.source_lang,
                    targetLang: glossaryInDb.target_lang,
                },
                entries: {},
            });
        } else {
            return res.status(StatusCodes.NOT_FOUND).send();
        }
    }

    let resGlossary = {
        ...glossary,
        sourceLang: glossary.sourceLang.toUpperCase(),
        targetLang: glossary.targetLang.toUpperCase(),
    };

    const entries = await translator.getGlossaryEntries(glossary!.glossaryId);

    return res.status(StatusCodes.OK).json({ glossary: resGlossary, entries });
}

// need to clean string for deepl api.
function cleanString(input: string): string {
    // Check for empty input
    if (!input) {
        throw new Error("Input string is empty");
    }

    // Remove C0 and C1 control characters
    // Unicode range for C0: U+0000 to U+001F and for C1: U+0080 to U+009F
    let result = input.replace(/[\u0000-\u001F\u0080-\u009F]/g, "");

    // Trim leading and trailing Unicode whitespace
    // \s in a RegExp includes [ \t\r\n\v\f\u00A0\u2028\u2029] and more
    result = result.trim();

    // Check if the result is empty after cleaning
    if (!result) {
        throw new Error("Resulting string is empty after cleaning");
    }

    return result;
}
