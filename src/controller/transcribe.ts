import { Request, Response } from "express";

import OpenAI from "openai";
import fs from "fs";
import util from "util";
import { exec } from "child_process";
import checkForBlockedPhrases from "../services/blockedPhrases";
import { v4 as uuidv4 } from "uuid"; // Import the UUID library to generate unique file names
import { DeeplSupportedLanguage } from "../models/DeeplSupportedLanguages";
import { TranscriptionCreateParams } from "openai/resources/audio/transcriptions";
import pronounciationWhisperSourceLang from "../services/whisper_pronounciation";
import { TranslationCreateParams } from "openai/resources/audio/translations";
import supabaseAdminClient from "../util/createClient";
import { saveAudioFile } from "../services/supabase/audio";

// Define the route for handling POST requests
export const transcribe = async (req: Request, res: Response) => {
    const { source_lang, target_lang } = req.query as {
        source_lang: string;
        target_lang: string;
    };

    const base64Audio = req.body.audio;

    try {
        const text = await convertAudioToText(
            Buffer.from(base64Audio, "base64"),
            source_lang
                ? (source_lang as DeeplSupportedLanguage)
                : DeeplSupportedLanguage.ES,
            req.user.id
        );

        const blockedPhrases = checkForBlockedPhrases(text);
        if (blockedPhrases.length > 0) {
            return res.status(200).json({
                error:
                    "Following phrases were forbidden: " +
                    blockedPhrases.join(", "),
            });
        } else {
            return res.status(200).json({ result: filterText(text) });
        }
    } catch (error) {
        console.error(`Error with OpenAI API request: ${error}`);
        return res
            .status(500)
            .json({ error: "An error occurred during your request." });
    }
};

// This function converts audio data to MP3 format using ffmpeg
async function convertAudioToMp3(audioData: any) {
    const execAsync = util.promisify(exec);
    const uniqueId = uuidv4(); // Generate a unique ID for this conversion operation
    const inputPath = `./tmp/input-${uniqueId}.webm`;
    const outputPath = `./tmp/output-${uniqueId}.mp3`;

    try {
        // Use asynchronous writeFile and readFile
        await fs.promises.writeFile(inputPath, audioData);
        await execAsync(`ffmpeg -i "${inputPath}" -ab 32k "${outputPath}"`);
        const mp3AudioData = await fs.promises.readFile(outputPath);
        // const fileSize = Buffer.byteLength(mp3AudioData);

        return mp3AudioData;
    } catch (error) {
        console.error("Convert audio to mp3 error", error);
        return undefined;
    } finally {
        // Ensure cleanup happens even if there's an error
        try {
            await fs.promises
                .unlink(inputPath)
                .catch((err) =>
                    console.error(`Error deleting input file: ${err}`)
                );
            await fs.promises
                .unlink(outputPath)
                .catch((err) =>
                    console.error(`Error deleting output file: ${err}`)
                );
        } catch (error) {
            console.error("Convert deleting input and output files", error);
            return undefined;
        }
    }
}

// This function converts audio data to text using the OpenAI API
async function convertAudioToText(
    audioData: any,
    language: DeeplSupportedLanguage,
    agentId: string
) {
    // Configure the OpenAI API client
    const configuration = {
        apiKey: process.env.OPENAI_API_KEY,
    };
    const openai = new OpenAI(configuration);

    const mp3AudioData = await convertAudioToMp3(audioData);
    const outputPath = "./tmp/output.mp3";
    try {
        fs.writeFileSync(outputPath, mp3AudioData!);
    } catch (error) {
        console.error("writeFile error", error);
    }
    const transcribeConfig: TranscriptionCreateParams = {
        file: fs.createReadStream(outputPath),
        model: "whisper-1",
        language: language.toLowerCase(),
        response_format: "json",
        temperature: 0,
        prompt: pronounciationWhisperSourceLang(language),
    };

    try {
        const transcription = await openai.audio.transcriptions.create(
            transcribeConfig
        );
        fs.unlinkSync(outputPath);
        const transcribedText = transcription.text;
        saveAudioFile(audioData, transcribedText, language, agentId);

        return transcribedText;
    } catch (error) {
        console.error("Error with OpenAI API request:", error);
        throw error;
    }
}

function filterText(input: string) {
    if (input.toLowerCase().includes("amara.org")) {
        return "";
    }

    return input;
}
