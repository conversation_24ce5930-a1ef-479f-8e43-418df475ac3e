import OpenAI from "openai";
import { sendLogSnag } from "../services/logsnag";
import supabaseAdminClient from "../util/createClient";

let openai: OpenAI | undefined = undefined;

const MODERATION_BATCH_SIZE = 32; // OpenAI's maximum allowed items per moderation request

async function moderationChecker(inputs: string[]) {
    const results = [];
    for (let i = 0; i < inputs.length; i += MODERATION_BATCH_SIZE) {
        const batch = inputs.slice(i, i + MODERATION_BATCH_SIZE);
        const moderation = await openai!.moderations.create({ input: batch });
        results.push(...moderation.results);
    }
    return results;
}

async function updateRows(client: any, rows: any[]) {
    const { error } = await client
        .from("chat_complete_analytics")
        .upsert(rows, { onConflict: "id" });

    if (error) {
        await sendLogSnag(
            "system",
            undefined,
            `Moderation update error:\n${error.message}`
        );
    }
}

export async function moderateNewFinetuningMessages() {
    const client = supabaseAdminClient();
    openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY, // Use environment variable for API key
    });

    const { data, error } = await client
        .from("chat_complete_analytics")
        .update({ moderation_status: "in_queue" })
        .eq("chat_complete_prompt_id", 29)
        // .is("moderation_status", null)
        .is("is_used_for_finetuning", true)
        .gte("created_at", "2024-08-17T23:59:00Z")
        .lt("created_at", "2024-10-24T23:59:00Z")
        .order("id", { ascending: false })
        // .limit(1000)
        .select();

    if (error) {
        await sendLogSnag(
            "system",
            undefined,
            `Moderation retrieve dataset error:\n${error.message}`
        );
        return;
    }

    if (!data) return;

    const batchSize = 50; // Process 50 rows at a time
    for (let i = 0; i < data.length; i += batchSize) {
        const batch = data.slice(i, i + batchSize);
        const allMessages = batch.flatMap((row) => [
            ...row.prev_messages
                .filter((msg: any) => msg.role !== "system" && msg.content)
                .map((msg: any) => msg.content),
            row.ai_generated_message,
        ]);

        let moderationResults: any[] = [];
        let retryCount = 0;
        const maxRetries = 5;
        const baseDelay = 1000; // 1 second

        while (retryCount < maxRetries) {
            try {
                moderationResults = await moderationChecker(allMessages);
                break; // If successful, exit the retry loop
            } catch (error: any) {
                if (error.code === "rate_limit_exceeded") {
                    retryCount++;
                    const delay = baseDelay * Math.pow(2, retryCount); // Exponential backoff
                    console.log(
                        `Rate limit reached. Retrying in ${
                            delay / 1000
                        } seconds...`
                    );
                    await new Promise((resolve) => setTimeout(resolve, delay));
                } else {
                    throw error; // If it's not a rate limit error, rethrow it
                }
            }
        }

        if (!moderationResults) {
            console.log(
                `Failed to get moderation results after ${maxRetries} retries. Skipping batch.`
            );
            continue;
        }

        const updatedRows = batch.map((row, index) => {
            const rowMessageCount =
                1 +
                row.prev_messages.filter(
                    (msg: any) => msg.role !== "system" && msg.content
                ).length;
            const startIndex = batch
                .slice(0, index)
                .reduce(
                    (acc, r) =>
                        acc +
                        1 +
                        r.prev_messages.filter(
                            (msg: any) => msg.role !== "system" && msg.content
                        ).length,
                    0
                );
            const rowModerationResults = moderationResults.slice(
                startIndex,
                startIndex + rowMessageCount
            );

            const aiModerationResult = rowModerationResults[0];
            const prevMessagesModerationResults = rowModerationResults.slice(1);

            const moderation = {
                flagged: aiModerationResult.flagged,
                moderation_res_ai_message: aiModerationResult.flagged
                    ? aiModerationResult.category_scores
                    : null,
            };

            const updatedPrevMessages = row.prev_messages.map(
                (msg: any, msgIndex: number) => {
                    if (msg.role !== "system" && msg.content) {
                        const msgModerationResult =
                            prevMessagesModerationResults[msgIndex];
                        if (
                            msgModerationResult &&
                            msgModerationResult.flagged
                        ) {
                            moderation.flagged = true;
                            return {
                                ...msg,
                                moderation_res:
                                    msgModerationResult.category_scores,
                            };
                        }
                    }
                    return msg;
                }
            );

            return {
                id: row.id,
                moderation_status: "done",
                prev_messages: updatedPrevMessages,
                moderation,
            };
        });

        await updateRows(client, updatedRows);
    }
}
