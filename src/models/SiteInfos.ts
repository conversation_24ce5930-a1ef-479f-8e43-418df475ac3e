import DailyInfo from "./DailyInfo";
import OpenAI from "openai";

export interface ImageAnalysisRequest {
    imageUrls: string[];
    role: "user" | "assistant";
    origin: string;
    type: "profile_pic" | "asset" | "inline_chat" | "private_asset";
    moderatorId?: string;
    userId?: string;
    parameters?: {
        maxTokens?: number;
        temperature?: number;
        topP?: number;
        topK?: number;
        [key: string]: any;
    };
}

export interface ImageAnalysisData {
    analysis: string;
    keywords: string[];
    model: "gemini" | "grok";
    imageUrl: string;
    processedAt: Date;
    resText?: string;
}

export interface ImageAnalysisResponse {
    success: boolean;
    data?: ImageAnalysisData[];
    error?: string;
}

export interface ExtractedMetadata {
    customerInfos?: string;
    moderatorInfos?: string;
    customerNotes?: string;
    moderatorNotes?: string;
    customerUpdates?: string;
    moderatorUpdates?: string;
    sexualOrientation?: string;

    // these ones are only in b3
    // these are the last updates that include the text "foto", "photo", "bild", "penis", "krank", "gebrochen", "storben", "verletzt"
    importantCustomerNotes?: string;
    importantModeratorNotes?: string;

    lastCustomerImageDescription?: string;

    importantNotes?: string;
    customerUsername?: string;
    moderatorUsername?: string;
    customerAge?: string;
    moderatorAge?: string;
    moderatorAgeGenderTypingStyle?: string;
    customerGender?: string;
    moderatorGender?: string;
    customerName?: string;
    moderatorName?: string;
    ins?: string;
    outs?: string;
    customerHasProfilePic?: string;
    moderatorHasProfilePic?: string;
    customerProfileText?: string;
    moderatorProfileText?: string;
    customerHasPictures?: string;
    moderatorHasPictures?: string;
    lastMessageTimeCustomer?: string;
    lastMessageTimeModerator?: string;
    sessionStart?: string;
    customerCity?: string;
    customerCityRedundanceChecker?: string;
    moderatorCity?: string;
    weather?: string;
    day?: string;
    date?: string;
    promptDay?: string;
    time?: string;
    dayTime?: string;
    upcomingHolidays?: string;
    recentHolidays?: string;
    todayHoliday?: string;
    currentSeason?: string;

    // all of these are image descriptions
    moderatorGalleryAnalysis?: string[];
    customerGalleryAnalysis?: string[];
    moderatorProfilePicAnalysis?: string;
    customerProfilePicAnalysis?: string;
    moderatorProfilePicKeywords?: string[];
    customerProfilePicKeywords?: string[];

    photoDescription?: string;
    minLength?: number;

    openaiMessages?: string;
    minLengthInstructions?: string;
}

export type PageType =
    | "test"
    | "cw"
    | "gold"
    | "whatsmeet"
    | "xloves"
    | "onlydates69"
    | "myloves"
    | "lacarna"
    | "avz"
    | "b3"
    | "wifu"
    | "df"
    | "fpc"
    | "flirtking"
    | "teddy"
    | "kizzle"
    | "love-room"
    | "route66"
    | "livecreator"
    | "pankek"
    | "translate"
    | "cherry"
    | "torchwood"
    | "single-jungle"
    | "justlo";

export type Message = {
    text: string;
    type: "received" | "sent" | "system";
    messageType: "image" | "text";
    timestamp?: Date;
    imageSrc?: string;
};

export interface UserInfo {
    id?: string;
    name?: string;
    gender: "male" | "female";
    username?: string;
    city?: string;
    postalCode?: string;
    country?: "DE" | "CH" | "AT";
    occupation?: string;
    education?: string;
    birthDate: {
        age?: number;
        date?: Date; // ISO Date string
    };
    hobbies?: string;
    music?: string;
    movies?: string;
    books?: string;
    sports?: string;
    activities?: string;
    relationshipStatus?: string;
    lookingFor?: string;
    hasProfilePic?: boolean;
    hasPictures?: boolean;
    profileText?: string;
    rawText?: string;
    physicalDescription?: string;
    eyeColor?: string;
    height?: string;
    bodyType?: string;
    smoking?: string;
    hasTattoo?: boolean;
    housing?: string;
    hasCar?: boolean;
    personality?: string;
    privateGallery?: string[];
}

export interface UserNotes {
    rawText?: string;
    name?: string;
    age?: string;
    relationshipStatus?: string;
    city?: string;
    postalCode?: string;
    country?: string;
    occupation?: string;
    lookingFor?: string;
    hobbies?: string;
    children?: string;
    family?: string;
    siblings?: string;
    preferences?: string;
    taboos?: string;
    pets?: string;
    zodiacSign?: string;
    heightWeight?: string;
    eyeColor?: string;
    appearance?: string;
    health?: string;
    personality?: string;
    tattoos?: string;
    piercings?: string;
    musicTaste?: string;
    movies?: string;
    food?: string;
    drinks?: string;
    sexualPreferences?: string;
    sexualTaboos?: string;
}

export interface Update {
    date?: Date;
    description: string;
}

/**
 * importantNotes: only in b3, we filter for "bild", "photo", "foto", "penis" because there are many useless information
 */
export interface SiteInfos {
    origin: PageType;
    url?: string;
    messages: Message[];
    accountId?: string;
    html: string;
    extensionVersion: string;
    metaData: {
        moderatorId?: string;
        customerId?: string;
        moderatorInfo: UserInfo;
        customerInfo: UserInfo;
        moderatorNotes?: UserNotes;
        customerNotes?: UserNotes;
        moderatorUpdates?: Update[];
        customerUpdates?: Update[];
        sessionStart?: Date;
        appSessionService?: string;
        ins?: number;
        outs?: number;
        type?: string;
        importantNotes?: string;
        alertBoxMessages?: string[];
        chatId?: string;
        minLength?: number;
        // all of these are urls
        customerProfilePic?: string;
        moderatorProfilePic?: string;
        customerGallery?: string[];
        moderatorGallery?: string[];
        assets?: string[];
        roomId?: string;
        rewriteConfig?: {
            attributes?: string[];
            rewriteAge?: string;
            rewriteGender?: string;
            moderatorUsername?: string;
        };
        sendToMaestro?: boolean;
    };
}

export interface ComputedMetadata {
    currentDayTime?: {
        day: string;
        date: string;
        time: string;
        dayTime: string;
        promptDay: string;
    };
    summary?: { user: any; assistant: any };
    alert?: string;
    nearbyCity?: { name: string; lat: number; long: number };
    customerCity?: { name: string; lat: number; long: number };
    runCount?: number;
    dailyInfo?: DailyInfo;
    imageDescription?: string;
}

export function keyToGermanPrefix(key: string, value?: boolean): string {
    const mapping = {
        name: "Name",
        username: "Username",
        relationshipStatus: "Beziehungsstatus",
        city: "Stadt",
        postalCode: "Postleitzahl",
        country: "Land",
        occupation: "Beruf",
        lookingFor: "Sucht",
        hobbies: "Hobbys",
        children: "Kinder",
        family: "Familie",
        siblings: "Geschwister",
        pets: "Haustiere",
        zodiacSign: "Sternzeichen",
        heightWeight: "Größe/Gewicht",
        eyeColor: "Augenfarbe",
        appearance: "Aussehen",
        health: "Gesundheit",
        personality: "Persönlichkeit",
        tattoos: "Tattoos",
        piercings: "Piercings",
        musicTaste: "Musikgeschmack",
        movies: "Filme",
        food: "Essen",
        drinks: "Trinken",
        sexualPreferences: "Vorlieben beim Sex",
        physicalDescription: "Körperbeschreibung",
        sexualTaboos: "Tabus beim Sex",
        preferences: "Vorlieben",
        taboos: "Tabus",
        smoking: "Raucht",
        height: "Größe",
        bodyType: "Körperbau",
        housing: "Wohnsituation",
        profileText: "Profiltext",
        personal: "Persönliche Beschreibung",
    };

    return mapping[key as keyof typeof mapping] ?? key;
}

// we need following fields in analytics

export interface AnalyticsItem {
    chat_complete_prompt_id: number;
    prev_messages: any[];
    origin_website: string;
    language: string;
    ai_generated_message: string;
    ai_model: string;
    metadata: any;
    account_id?: string;
    corrected_output?: string;
    ins?: number;
    outs?: number;
    sessionStart?: Date;
    customerAge?: number;
    moderatorAge?: number;
    customerGender?: string;
    moderatorGender?: string;
    customerHasProfilePic?: boolean;
    moderatorHasProfilePic?: boolean;
    customerHasPictures?: boolean;
    moderatorHasPictures?: boolean;
    day?: string;
    dayTime?: string;
    promptDay?: string;
    relativeLastMessageTimeCustomer?: string;
    relativeLastMessageTimeModerator?: string;
    upcomingHolidays?: string;
    recentHolidays?: string;
    todayHoliday?: string;
    currentSeason?: string;
    weather?: string;
}

/**
 * ins
 * outs
 * sessionStart
 * customerAge
 * moderatorAge
 * customerGender
 * moderatorGender
 * customerHasProfilePic
 * moderatorHasProfilePic
 * customerHasPictures
 * moderatorHasPictures
 * day
 * dayTime
 * promptDay
 * lastMessageTimeCustomer
 * lastMessageTimeModerator
 * upcomingHolidays
 * recentHolidays
 * todayHoliday
 * currentSeason
 * weather
 */
