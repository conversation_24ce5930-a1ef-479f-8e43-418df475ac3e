export type PageType =
| "cw"
| "gold"
| "avz"
| "diamond"
| "b3"
| "wifu"
| "df"
| "fpc"
| "flirtking";

export type Message = {
text: string;
type: "received" | "sent" | "system";
messageType: "image" | "text";
timestamp?: Date;
imageSrc?: string;
};

export interface UserInfo {
name: string;
gender: "male" | "female";
username?: string;
city?: string;
postalCode?: string;
country?: "DE" | "CH" | "AT";
occupation?: string;
education?: string;
birthDate: {
age?: number;
date?: Date; // ISO Date string
};
hobbies?: string;
relationshipStatus?: string;
lookingFor?: string;
hasProfilePic?: boolean;
hasPictures?: boolean;
profileText?: string;
rawText?: string;
physicalDescription?: string;
eyeColor?: string;
height?: string;
bodyType?: string;
smoking?: string;
hasTattoo?: boolean;
housing?: string;
hasCar?: boolean;
personality?: string;
}

export interface UserNotes {
rawText?: string;
name?: string;
age?: string;
relationshipStatus?: string;
city?: string;
postalCode?: string;
country?: string;
occupation?: string;
lookingFor?: string;
hobbies?: string;
children?: string;
family?: string;
siblings?: string;
preferences?: string;
taboos?: string;
}

export interface Update {
date?: Date;
description: string;
}

/\*\*

-   importantNotes: only in b3, we filter for "bild", "photo", "foto", "penis" because there are many useless information
    \*/
    export interface SiteInfos {
    origin: PageType;
    messages: Message[];
    accountId?: string;
    html: string;
    metaData: {
    moderatorInfo: UserInfo;
    customerInfo: UserInfo;
    moderatorNotes?: UserNotes;
    customerNotes?: UserNotes;
    moderatorUpdates?: Update[];
    customerUpdates?: Update[];
    sessionStart?: Date;
    appSessionService?: string;
    ins?: number;
    outs?: number;
    type?: string;
    importantNotes?: string;
    alertBoxMessages?: string[];
    };
    }

export interface ComputedMetadata {
currentDayTime?: {
day: string;
time: string;
dayTime: string;
promptDay: string;
};
summary?: { user: any; assistant: any };
alert?: string;
nearbyCity?: string;
runCount?: number;
dailyInfo?: DailyInfo;
imageDescription?: string;
}

## customerInfos

## moderatorInfos

## customerNotes

## moderatorNotes

## customerNotesReactivation

## moderatorNotesReactivation

## importantCustomerNotes

## importantModeratorNotes

## importantNotes

## customerUsername

## moderatorUsername

## customerAge

## moderatorAge

## customerGender

## moderatorGender

## ins

## outs

## customerHasProfilePic

## moderatorHasProfilePic

## customerHasPictures

## moderatorHasPictures

## lastMessageTimeCustomer

## lastMessageTimeModerator

## sessionStart

## customerCity

## moderatorCity

## weather

## timeOfDay

## day

## time

## dayTime

## upcomingHolidays
