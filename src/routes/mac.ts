import * as express from "express";
import { Request, Response, NextFunction } from "express";
import { Mac } from "../models/Mac";
import { getMacsDB, updateMacDB } from "../services/supabase/macs";
import { getMacDB } from "../services/supabase/macs";

const macRouter = express.Router();

// Register/update machine status
macRouter.get("/restart-status", async (req: Request, res: Response) => {
    const hostname = req.headers["x-hostname"] as string;

    if (!hostname) {
        return res.status(400).json({ error: "Hostname required" });
    }

    let mac = await getMacDB(hostname);

    if (!mac) {
        mac = {
            hostname,
            shouldRestart: false,
            lastSeen: new Date(),
            shouldRestartSplashtop: false,
        };
    }

    mac.lastSeen = new Date();

    try {
        await updateMacDB(mac);
    } catch (error) {
        console.error(`Error updating Mac status for ${hostname}:`, error);
        return res.status(500).json({ error });
    }

    res.json({
        shouldRestart: mac.shouldRestart,
        shouldRestartSplashtop: mac.shouldRestartSplashtop,
    });
});

macRouter.post(
    "/restart-status/acknowledge",
    async (req: Request, res: Response) => {
        const hostname = req.headers["x-hostname"] as string;

        if (!hostname) {
            return res.status(400).json({ error: "Invalid hostname" });
        }

        const mac = await getMacDB(hostname);
        if (!mac) {
            return res.status(400).json({ error: "Invalid hostname" });
        }

        mac.shouldRestart = false;
        mac.shouldRestartSplashtop = false;
        try {
            await updateMacDB(mac);
        } catch (error) {
            console.error(
                `Error acknowledging restart for ${hostname}:`,
                error
            );
            return res.status(500).json({ error });
        }

        res.json({ status: "acknowledged" });
    }
);

export async function requestRestart(hostname: string) {
    const mac = await getMacDB(hostname);
    if (!mac) {
        throw new Error("Machine not found");
    }

    mac.shouldRestart = true;
    try {
        await updateMacDB(mac);
    } catch (error: any) {
        console.error(`Error requesting restart for ${hostname}:`, error);
        throw new Error(`Failed to request restart: ${error.message}`);
    }
}

export async function requestRestartSplashtop(hostname: string) {
    const mac = await getMacDB(hostname);
    if (!mac) {
        throw new Error("Machine not found");
    }

    mac.shouldRestartSplashtop = true;
    try {
        await updateMacDB(mac);
    } catch (error: any) {
        console.error(
            `Error requesting Splashtop restart for ${hostname}:`,
            error
        );
        throw new Error(
            `Failed to request Splashtop restart: ${error.message}`
        );
    }
}

macRouter.post(
    "/request-restart/:hostname",
    async (req: Request, res: Response) => {
        const hostname = req.params.hostname as string;

        if (!hostname) {
            return res.status(400).json({ error: "Invalid hostname" });
        }

        const mac = await getMacDB(hostname);
        if (!mac) {
            return res.status(404).json({ error: "Machine not found" });
        }

        mac.shouldRestart = true;
        try {
            await updateMacDB(mac);
        } catch (error) {
            console.error(
                `Error requesting restart via API for ${hostname}:`,
                error
            );
            return res.status(500).json({ error });
        }

        res.json({ status: "restart requested" });
    }
);

macRouter.get("/machines", async (req: Request, res: Response) => {
    const macs = await getMacsDB();

    if (!macs) {
        return res.status(404).json({ error: "No machines found" });
    }

    const machineList = macs.map((mac) => ({
        hostname: mac.hostname,
        lastSeen: mac.lastSeen,
        shouldRestart: mac.shouldRestart,
        isOnline: mac.lastSeen
            ? new Date().getTime() - mac.lastSeen.getTime() < 30000
            : false,
    }));

    res.json(machineList);
});

export default macRouter;
