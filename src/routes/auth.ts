import * as express from "express";
import { login, register, refreshToken, logout } from "../controller/auth";
import authMiddleware from "../middleware/auth";

const authRouter = express.Router();

// Public routes
authRouter.post("/login", login);
authRouter.post("/register", register);
authRouter.post("/refresh", refreshToken);

// Protected routes
authRouter.post("/logout", authMiddleware, logout);

export default authRouter;
