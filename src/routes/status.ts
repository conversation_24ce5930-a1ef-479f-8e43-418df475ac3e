import { Request, Response } from "express";
import * as express from "express";
import supabaseAdminClient from "../util/createClient";

const statusRouter = express.Router();

statusRouter.post("/", logStatusExtension);

export default statusRouter;

async function logStatusExtension(req: Request, res: Response) {
    const { origin, reason } = req.body;

    const supabase = supabaseAdminClient();

    const { data, error } = await supabase.from("log_status_extension").insert({
        origin,
        reason,
    });

    return res.status(200);
}
