import dayjs from "dayjs";

export function computeNaturalTime(date: Date): string {
    const now = dayjs();
    const messageDate = dayjs(date);

    if (!messageDate.isValid()) return "";

    const isYesterday =
        messageDate.date() !== now.date() ||
        messageDate.month() !== now.month() ||
        messageDate.year() !== now.year();

    const diffInDays = now.diff(messageDate, "day");
    const messageHour = messageDate.hour();
    const timeOfDay = getTimeOfDay(messageHour);

    if (!isYesterday && diffInDays === 0) {
        return `heute ${timeOfDay}`;
    } else if (diffInDays <= 1) {
        return `gestern ${timeOfDay}`;
    } else if (diffInDays === 2) {
        return `vorgestern ${timeOfDay}`;
    } else {
        return computeRelativeTime(date);
    }
}

export function computeRelativeTime(date: Date): string {
    const now = dayjs();
    const dayjsDate = dayjs(date);

    if (dayjsDate.isValid()) {
        const diffInSeconds = now.diff(dayjsDate, "second");
        const isInFuture = diffInSeconds < 0;
        const absDiffInSeconds = Math.abs(diffInSeconds);
        const prefix = isInFuture ? "in" : "vor";
        let relativeTime;

        if (absDiffInSeconds < 60) {
            relativeTime = isInFuture
                ? "in wenigen Sekunden"
                : "vor wenigen Sekunden";
        } else if (absDiffInSeconds < 3600) {
            relativeTime = `${prefix} ${Math.floor(
                absDiffInSeconds / 60
            )} Minuten`;
        } else if (absDiffInSeconds < 86400) {
            relativeTime = `${prefix} ${Math.floor(
                absDiffInSeconds / 3600
            )} Stunden`;
        } else if (absDiffInSeconds < 604800) {
            relativeTime = `${prefix} ${Math.floor(
                absDiffInSeconds / 86400
            )} Tagen`;
        } else if (absDiffInSeconds < 2592000) {
            relativeTime = `${prefix} ${Math.floor(
                absDiffInSeconds / 604800
            )} Wochen`;
        } else {
            relativeTime = `${prefix} ${Math.floor(
                absDiffInSeconds / 2592000
            )} Monaten`;
        }

        return relativeTime;
    }

    return "";
}

export function getTimeOfDay(hour: number): string {
    if (hour >= 5 && hour < 11) return "Morgen";
    if (hour >= 11 && hour < 14) return "Mittag";
    if (hour >= 14 && hour < 18) return "Nachmittag";
    if (hour >= 18 && hour < 22) return "Abend";
    return "Nacht";
}
