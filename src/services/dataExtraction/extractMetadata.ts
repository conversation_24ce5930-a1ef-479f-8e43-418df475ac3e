// We need this to extract metadata from the SiteInfos and the chats

import {
    ComputedMetadata,
    ExtractedMetadata,
    keyToGermanPrefix,
    SiteInfos,
    Update,
    UserNotes,
} from "../../models/SiteInfos";
import { computeNaturalTime } from "./dateTransformer";
import {
    extractBirthday,
    extractModeratorAgeGenderTypingStyle,
} from "./notesExtraction";
import { PromptType } from "../promptType/promptTypeChatCompletion";
import { structureNotesFromRawtext } from "./structureNotesFromRawtext";
import { analyzeImage } from "../vison/visionServer";
const keysToExclude = [
    "hasProfilePic",
    "hasPictures",
    "birthDate",
    "age",
    "gender",
    "username",
    "city",
    "rawText",
    "profileText",
    "privateGallery",
];

const keyToExcludeForReactivation = [...keysToExclude, "health"];

const keysToExcludeForReactivationNewCustomer = [
    ...keysToExclude,
    "health",
    "hobbies",
];

/**
 * TODO
 * if there is a date in an update, do not include it
 * if there is a comma after the name, remove the items after the comma
 */

function processNotesReactivation(notes: any, newCustomer: boolean) {
    if (!notes) return;

    const usedKeys = [
        "name",
        ...Object.keys(notes)
            .filter((key) => key !== "name" && key !== "age")
            .sort(() => Math.random() - 0.5)
            .slice(0, 1),
    ];

    const keysToExclude = newCustomer
        ? keysToExcludeForReactivationNewCustomer
        : keyToExcludeForReactivation;

    return usedKeys
        .filter((key) => !keysToExclude.includes(key))
        .map((key) => {
            const value = notes[key as keyof typeof notes];
            if (!value) return null;

            if (typeof value === "boolean") {
                const booleanMappings = {
                    hasTattoo: ["hat einen Tattoo", "hat keinen Tattoo"],
                    hasCar: ["hat ein Auto", "hat kein Auto"],
                };
                return booleanMappings[key as keyof typeof booleanMappings]?.[
                    value ? 0 : 1
                ];
            }

            return `${keyToGermanPrefix(key)}: ${value}`;
        })
        .filter(Boolean)
        .join("; ");
}

function processNotes(notes: any, siteInfos: SiteInfos) {
    if (!notes) return;

    let _keysToExclude = [...keysToExclude];
    if (notes.username && siteInfos.origin == "torchwood") {
        _keysToExclude = _keysToExclude.filter((key) => key !== "username");
    }

    return Object.keys(notes)
        .filter((key) => !_keysToExclude.includes(key))
        .map((key) => {
            const value = notes[key as keyof typeof notes];
            if (!value) return null;

            if (typeof value === "boolean") {
                const booleanMappings = {
                    hasTattoo: ["hat Tattoos", "hat keine Tattoos"],
                    hasCar: ["hat ein Auto", "hat kein Auto"],
                };
                return booleanMappings[key as keyof typeof booleanMappings]?.[
                    value ? 0 : 1
                ];
            }

            return `${keyToGermanPrefix(key)}: ${value}`;
        })
        .filter(Boolean)
        .join("; ");
}

// important for b3
function extractImportantNotes(updates?: Update[]): string | undefined {
    if (!updates || updates.length === 0) return;
    const wordsToBeIncluded = [
        "foto",
        "photo",
        "bild",
        "penis",
        "krank",
        "gebrochen",
        "storben",
        "verletzt",
    ];
    if (!updates) return;

    const importantNotes = updates
        .filter((update) => {
            // Only include updates from last 2 days
            if (!update.date) return false;
            const date = new Date(update.date);
            const diffTime = Math.abs(date.getTime() - new Date().getTime());
            const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
            if (diffDays > 2) return false;

            return wordsToBeIncluded.some((word) =>
                update.description?.includes(word)
            );
        })
        .slice(0, 3);

    return importantNotes.length > 0
        ? importantNotes
              .map((update) => {
                  return `${computeNaturalTime(new Date(update.date!))}: ${
                      update.description
                  }`;
              })
              .join("; ")
        : undefined;
}

function createLastMessageTimeText(
    userType: "customer" | "moderator",
    lastMessageTime?: Date
): string | undefined {
    if (!lastMessageTime)
        return "Dein Gesprächspartner hat dir bisher noch keine Nachricht gesendet.";
    if (userType === "customer") {
        return `Zeitpunkt der letzten Antwort deines Gesprächspartners: Die letzte Antwort deines Gesprächsparteners kam ${computeNaturalTime(
            lastMessageTime
        )} an. Dir wurde das letzte Mal ${computeNaturalTime(
            lastMessageTime
        )} auf deine Nachricht geantwortet.`;
    } else {
        return `Deine letzte Nachricht hast du ${computeNaturalTime(
            lastMessageTime
        )} gesendet.`;
    }
}

function reactivationUpdatesDateFilter(update: Update): boolean {
    if (!update.date) {
        return false;
    }
    const date = new Date(update.date);
    const diffTime = Math.abs(date.getTime() - new Date().getTime());
    const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 2;
}

function updatesDateFilter(update: Update): boolean {
    if (!update.date) {
        return false;
    }
    const date = new Date(update.date);
    const diffTime = Math.abs(date.getTime() - new Date().getTime());
    const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 7;
}

function updateToText(update: Update): string {
    return (
        computeNaturalTime(new Date(update.date!)) + ": " + update.description
    );
}

export const extractMetadata = async (
    siteInfos: SiteInfos,
    computedMetadata: ComputedMetadata,
    type: PromptType,
    customerNotesFromRawText: UserNotes | undefined,
    moderatorNotesFromRawText: UserNotes | undefined
): Promise<ExtractedMetadata> => {
    const { messages, metaData, origin } = siteInfos;
    const { currentDayTime, dailyInfo, imageDescription, nearbyCity } =
        computedMetadata;
    let {
        moderatorInfo,
        customerInfo,
        moderatorUpdates,
        customerUpdates,
        sessionStart,
        ins,
        outs,
        importantNotes,
        alertBoxMessages,
        moderatorNotes,
        customerNotes,
    } = metaData;

    // this is the object that will be used to merge the prompt
    let extractedMetadata: ExtractedMetadata = {
        ins: ins
            ? `Du hast ${ins} Nachrichten von deinem Gesprächspartner erhalten.`
            : undefined,
        outs: outs
            ? `Du hast ${outs} Nachrichten an deinen Gesprächspartner gesendet.`
            : undefined,
        day: currentDayTime?.day,
        date: currentDayTime?.date,
        time: currentDayTime?.time,
        dayTime: currentDayTime?.dayTime,
        promptDay: currentDayTime?.promptDay,
        upcomingHolidays: dailyInfo?.holidays?.upcomingHolidays?.length
            ? `Feiertage, die anstehen: ${dailyInfo.holidays.upcomingHolidays
                  .map((h) => h.local_name)
                  .join(", ")}`
            : undefined,
        recentHolidays: dailyInfo?.holidays?.recentHolidays?.length
            ? `Kürzlich vergangene Feiertage: ${dailyInfo.holidays.recentHolidays
                  .map((h) => h.local_name)
                  .join(", ")}`
            : undefined,
        customerProfileText:
            customerInfo.profileText || "Kein Profiltext angegeben",
        moderatorProfileText: moderatorInfo.profileText,
        todayHoliday:
            "Heute ist " +
            (dailyInfo?.holidays?.todayHoliday?.local_name ??
                "kein Feiertag.") +
            ".",
        currentSeason: dailyInfo?.holidays.currentSeason,
        weather: dailyInfo?.weatherData,
    };

    extractedMetadata.customerInfos = processNotes(customerInfo, siteInfos);
    extractedMetadata.moderatorInfos = processNotes(moderatorInfo, siteInfos);

    // if username is name, remove the name
    if (customerInfo.username === customerInfo.name) {
        customerInfo.name = undefined;
    }
    if (moderatorInfo.username === moderatorInfo.name) {
        moderatorInfo.name = undefined;
    }
    if (customerNotes && customerNotes.name === customerInfo.username) {
        customerNotes.name = undefined;
    }
    if (moderatorNotes && moderatorNotes.name === moderatorInfo.username) {
        moderatorNotes.name = undefined;
    }

    if (siteInfos.origin === "b3") {
        // these ones are only in b3
        // these are the last updates that include the text "foto", "photo", "bild", "penis", "krank", "gebrochen", "storben", "verletzt"
        extractedMetadata.importantCustomerNotes =
            extractImportantNotes(customerUpdates);
        extractedMetadata.importantModeratorNotes =
            extractImportantNotes(moderatorUpdates);
    }

    let mergedCustomerNotes: UserNotes | undefined;
    let mergedModeratorNotes: UserNotes | undefined;

    // Merge with existing notes, prioritizing extracted data
    if (customerNotes?.rawText && customerNotesFromRawText) {
        mergedCustomerNotes = {
            ...customerNotes,
            ...customerNotesFromRawText, // GPT extracted data takes priority
            rawText: customerNotes.rawText, // Preserve rawText
        };
    } else if (customerNotesFromRawText) {
        // Use extracted notes even if no rawText exists in original
        mergedCustomerNotes = {
            ...customerNotes,
            ...customerNotesFromRawText,
        };
    } else {
        mergedCustomerNotes = customerNotes;
    }

    if (moderatorNotes?.rawText && moderatorNotesFromRawText) {
        mergedModeratorNotes = {
            ...moderatorNotes,
            ...moderatorNotesFromRawText, // GPT extracted data takes priority
            rawText: moderatorNotes.rawText, // Preserve rawText
        };
    } else if (moderatorNotesFromRawText) {
        // Use extracted notes even if no rawText exists in original
        mergedModeratorNotes = {
            ...moderatorNotes,
            ...moderatorNotesFromRawText,
        };
    } else {
        mergedModeratorNotes = moderatorNotes;
    }

    if (customerNotes?.name && customerNotes?.name?.trim() !== "") {
        if (
            customerNotes.name.trim().split(" ").length == 1 &&
            customerNotes.name.trim().split(",").length == 1 &&
            siteInfos.metaData.ins &&
            siteInfos.metaData.ins > 100
        ) {
            // for long term customers use the name only if it is a single word
            extractedMetadata.customerName = `Der Name deines Gesprächspartners ist ${customerNotes.name}.`;
        } else if (siteInfos.metaData.ins && siteInfos.metaData.ins < 100) {
            // for short term customers use the name whatever is in there
            extractedMetadata.customerName = `Der Name deines Gesprächspartners ist ${customerNotes.name}.`;
        } else if (!siteInfos.metaData.ins) {
            // if we dont have ins, use the name whatever is in there
            extractedMetadata.customerName = `Der Name deines Gesprächspartners ist ${customerNotes.name}.`;
        } else if (
            customerNotes.name.trim().split(" ").length > 1 ||
            customerNotes.name.trim().split(",").length > 1
        ) {
            extractedMetadata.customerName = ``;
        }
    } else if (customerInfo?.name && customerInfo?.name?.trim() !== "") {
        extractedMetadata.customerName = `Der Name deines Gesprächspartners ist ${customerInfo.name}.`;
    }

    if (moderatorNotes?.name && moderatorNotes?.name?.trim() !== "") {
        extractedMetadata.moderatorName = `Dein Name ist ${moderatorNotes.name}.`;
    } else if (moderatorInfo?.name && moderatorInfo?.name?.trim() !== "") {
        extractedMetadata.moderatorName = `Dein Name ist ${moderatorInfo.name}.`;
    }

    // MARK:
    /**
     * - Updates only from the last 48 hours
     * - If updates include a date, do not include them#
     * - Transform dates to strings in the format of "x days ago" or "x hours ago"
     */
    if (type?.includes("reactivate")) {
        // 1. Updates
        if (customerUpdates) {
            const filteredCustomerUpdates = customerUpdates
                .filter(reactivationUpdatesDateFilter)
                .map(updateToText);
            extractedMetadata.customerUpdates =
                filteredCustomerUpdates.length > 0
                    ? filteredCustomerUpdates.join("; ")
                    : undefined;
        }

        if (moderatorUpdates) {
            const filteredModeratorUpdates = moderatorUpdates
                .filter(reactivationUpdatesDateFilter)
                .map(updateToText);
            extractedMetadata.moderatorUpdates =
                filteredModeratorUpdates.length > 0
                    ? filteredModeratorUpdates.join("; ")
                    : undefined;
        }

        // 2. Notes
        let newCustomer: boolean = type == PromptType.REACTIVATE_NEW_USER;

        if (mergedCustomerNotes) {
            extractedMetadata.customerNotes = processNotesReactivation(
                mergedCustomerNotes,
                newCustomer
            );
        }
        if (mergedModeratorNotes) {
            extractedMetadata.moderatorNotes = processNotesReactivation(
                mergedModeratorNotes,
                newCustomer
            );
        }
    } else {
        // 1. Updates
        if (customerUpdates) {
            const filteredCustomerUpdates = customerUpdates
                .filter(updatesDateFilter)
                .map(updateToText);
            extractedMetadata.customerUpdates =
                filteredCustomerUpdates.length > 0
                    ? filteredCustomerUpdates.join("; ")
                    : undefined;
        }

        if (moderatorUpdates) {
            const filteredModeratorUpdates = moderatorUpdates
                .filter(updatesDateFilter)
                .map(updateToText);
            extractedMetadata.moderatorUpdates =
                filteredModeratorUpdates.length > 0
                    ? filteredModeratorUpdates.join("; ")
                    : undefined;
        }

        // 2. Notes
        if (mergedCustomerNotes) {
            extractedMetadata.customerNotes = processNotes(
                mergedCustomerNotes,
                siteInfos
            );
        }
        if (mergedModeratorNotes) {
            extractedMetadata.moderatorNotes = processNotes(
                mergedModeratorNotes,
                siteInfos
            );
        }
    }

    // We need to add prefixes to the notes.
    // Notes are always the information that the customer knows about the moderator and vice versa
    if (
        extractedMetadata.customerNotes &&
        extractedMetadata.customerNotes !== ""
    ) {
        extractedMetadata.customerNotes =
            "Hier sind Informationen, die du von deinem Gesprächspartner erfahren hast: " +
            extractedMetadata.customerNotes +
            ". ";
    } else if (origin !== "df") {
        extractedMetadata.customerNotes =
            "Du weißt noch nichts über deinen Gesprächspartner. ";
    } else {
        extractedMetadata.customerNotes = undefined;
    }

    if (
        extractedMetadata.moderatorNotes &&
        extractedMetadata.moderatorNotes !== ""
    ) {
        extractedMetadata.moderatorNotes =
            "Hier sind Informationen, die dein Gesprächspartner über dich erfahren hat: " +
            extractedMetadata.moderatorNotes +
            ". ";
    } else if (origin !== "df") {
        extractedMetadata.moderatorNotes =
            "Dein Gesprächspartner weiß noch nichts über dich. ";
    } else {
        extractedMetadata.moderatorNotes = undefined;
    }

    if (messages.length > 0) {
        let customerMessages = messages.filter(
            (message) => message.type == "received"
        );
        let moderatorMessages = messages.filter(
            (message) => message.type == "sent"
        );
        extractedMetadata.lastMessageTimeCustomer = createLastMessageTimeText(
            "customer",
            customerMessages[customerMessages.length - 1]?.timestamp
        );
        extractedMetadata.lastMessageTimeModerator = createLastMessageTimeText(
            "moderator",
            moderatorMessages[moderatorMessages.length - 1]?.timestamp
        );
    }

    extractedMetadata.lastCustomerImageDescription = imageDescription;
    extractedMetadata.customerAge = extractBirthday("customer", siteInfos);
    extractedMetadata.moderatorAge = extractBirthday("moderator", siteInfos);

    extractedMetadata.customerGender = customerInfo.gender
        ? customerInfo.gender?.toLowerCase() == "male"
            ? "Mann"
            : "Frau"
        : undefined;
    extractedMetadata.moderatorGender = moderatorInfo.gender
        ? moderatorInfo.gender?.toLowerCase() == "male"
            ? "Mann"
            : "Frau"
        : undefined;

    let sexualOrientation = "";

    if (
        customerInfo.gender &&
        moderatorInfo.gender &&
        customerInfo.gender.toLowerCase() == "male"
    ) {
        if (moderatorInfo.gender.toLowerCase() == "male") {
            sexualOrientation = "Both are gay";
        } else {
            sexualOrientation = "Both are straight and not homosexual";
        }
    } else if (
        customerInfo.gender &&
        moderatorInfo.gender &&
        customerInfo.gender.toLowerCase() == "female"
    ) {
        if (moderatorInfo.gender.toLowerCase() == "female") {
            sexualOrientation = "Both are lesbians";
        } else {
            sexualOrientation = "Both are straight and not homosexual";
        }
    } else {
        sexualOrientation = "Both are straight and not homosexual";
    }

    extractedMetadata.sexualOrientation = sexualOrientation;

    // if username is name, remove the name
    // username will for moderator or customer will only be available if both info and notes have no name
    if (!customerInfo.name && !customerNotes?.name) {
        extractedMetadata.customerUsername = customerInfo.username;
    }

    if (!moderatorInfo.name && !moderatorNotes?.name) {
        extractedMetadata.moderatorUsername = moderatorInfo.username;
    }

    if (customerNotes?.city) {
        extractedMetadata.customerCity = `Dein Gesprächspartner kommt aus ${customerNotes.city}.`;

        extractedMetadata.customerCityRedundanceChecker = `Dein Gesprächspartner kommt aus ${customerNotes.city}.`;
    } else if (customerInfo.city) {
        extractedMetadata.customerCity = `Dein Gesprächspartner kommt aus ${customerInfo.city}.`;

        if (
            origin == "df" ||
            origin == "b3" ||
            origin == "fpc" ||
            origin == "love-room" ||
            origin == "teddy"
        ) {
            extractedMetadata.customerCityRedundanceChecker = `Dein Gesprächspartner kommt aus ${customerInfo.city}.`;
        }
    }

    if (moderatorNotes?.city) {
        extractedMetadata.moderatorCity = `Du wohnst in ${moderatorNotes.city}. Wenn du gefragt wirst, wo du wohnst, antworte, dass du in ${moderatorNotes.city} wohnst.`;
    } else if (moderatorInfo.city) {
        extractedMetadata.moderatorCity = `Du wohnst in ${moderatorInfo.city}. Wenn du gefragt wirst, wo du wohnst, antworte, dass du in ${moderatorInfo.city} wohnst.`;
    } else if (nearbyCity) {
        extractedMetadata.moderatorCity = `Du wohnst in ${nearbyCity.name}. Wenn du gefragt wirst, wo du wohnst, antworte, dass du in ${nearbyCity.name} wohnst.`;
    }

    if (moderatorNotes?.city) {
        if (moderatorNotes.city.toLowerCase() == "no city mentioned") {
            extractedMetadata.moderatorCity = undefined;
        }
    }

    if (customerNotes?.city) {
        if (customerNotes.city.toLowerCase() == "no city mentioned") {
            extractedMetadata.customerCity = undefined;
        }
    }

    if (customerInfo.hasProfilePic != undefined) {
        let text = customerInfo.hasProfilePic
            ? "Dein Gesprächspartner hat ein Profilbild auf seinem Profil."
            : "Dein Gesprächspartner hat kein Profilbild auf seinem Profil.";
        extractedMetadata.customerHasProfilePic = text;
    } else {
        extractedMetadata.customerHasProfilePic = undefined;
    }

    if (moderatorInfo.hasProfilePic != undefined) {
        let text = moderatorInfo.hasProfilePic
            ? "Du hast ein Profilbild auf deinem Profil."
            : "Du hast kein Profilbild auf deinem Profil.";
        extractedMetadata.moderatorHasProfilePic = text;
    } else {
        extractedMetadata.moderatorHasProfilePic = undefined;
    }

    if (customerInfo.hasPictures != undefined) {
        let text = customerInfo.hasPictures
            ? "Dein Gesprächspartner hat Fotos von sich selbst auf seinem Profil. "
            : "Dein Gesprächspartner hat keine Fotos von sich selbst auf seinem Profil. ";
        extractedMetadata.customerHasPictures = text;
    } else {
        extractedMetadata.customerHasPictures = undefined;
    }

    if (sessionStart) {
        extractedMetadata.sessionStart = `Zeitpunkt Kennenlernen: Eure erste Interaktion war ${computeNaturalTime(
            sessionStart
        )} (Erstkontakt) - Ihr kennt euch also seit ${computeNaturalTime(
            sessionStart
        )}.`;
    }

    extractedMetadata.moderatorAgeGenderTypingStyle =
        extractModeratorAgeGenderTypingStyle(siteInfos);

    // TODO: - text für Kein profilbild
    if (siteInfos.metaData.customerProfilePic) {
        const customerProfilePicAnalysis = await analyzeImage({
            imageUrls: [siteInfos.metaData.customerProfilePic],
            origin: siteInfos.origin,
            type: "profile_pic",
            role: "user",
        });

        extractedMetadata.customerProfilePicAnalysis =
            customerProfilePicAnalysis?.data?.[0]?.analysis;

        if (extractedMetadata.customerProfilePicAnalysis) {
            extractedMetadata.customerProfilePicAnalysis =
                "Hier sind infos über das Profilbild deines Gesprächspartners: " +
                extractedMetadata.customerProfilePicAnalysis;
            extractedMetadata.customerHasProfilePic = undefined;
        }

        if (customerProfilePicAnalysis?.data?.[0]?.keywords) {
            extractedMetadata.customerProfilePicKeywords =
                customerProfilePicAnalysis?.data?.[0]?.keywords;
        }
    }

    if (siteInfos.metaData.moderatorProfilePic) {
        const moderatorProfilePicAnalysis = await analyzeImage({
            imageUrls: [siteInfos.metaData.moderatorProfilePic],
            origin: siteInfos.origin,
            type: "profile_pic",
            role: "assistant",
        });
        extractedMetadata.moderatorProfilePicAnalysis =
            moderatorProfilePicAnalysis?.data?.[0]?.analysis;

        if (extractedMetadata.moderatorProfilePicAnalysis) {
            extractedMetadata.moderatorProfilePicAnalysis =
                "Hier sind infos über dein Profilbild: " +
                extractedMetadata.moderatorProfilePicAnalysis;
            extractedMetadata.moderatorHasProfilePic = undefined;
        }

        if (moderatorProfilePicAnalysis?.data?.[0]?.keywords) {
            extractedMetadata.moderatorProfilePicKeywords =
                moderatorProfilePicAnalysis?.data?.[0]?.keywords;
        }
    }

    extractedMetadata.minLength = siteInfos.metaData.minLength;

    if (siteInfos.metaData.minLength) {
        if (siteInfos.metaData.minLength > 200) {
            extractedMetadata.minLengthInstructions =
                "Nachrichtenlänge: 4 Sätze. Ziel sind 40 Wörter. Die Obergrenze von 250 Zeichen sollte erreicht werden.";
        } else if (siteInfos.metaData.minLength < 200) {
            extractedMetadata.minLengthInstructions = `Nachrichtenlänge: 3 Sätze. Ziel sind 30 Wörter. Die Obergrenze von ca. 200 Zeichen sollte selten erreicht werden.`;
        }
    }

    return extractedMetadata;
};
