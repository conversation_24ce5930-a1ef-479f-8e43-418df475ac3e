// use openai json mode
import <PERSON><PERSON><PERSON> from "openai";
import { z } from "zod";
import { zodResponseFormat } from "openai/helpers/zod";
import { PageType, UserNotes } from "../../models/SiteInfos";
import dayjs from "dayjs";
import supabaseAdminClient from "../../util/createClient";
import { createAnalyticsItem } from "../supabase/chatCompleteAnalyticsService";
import { getPromptDB } from "../supabase/promptConfig";

const supabase = supabaseAdminClient();

const UserNotesExtraction = z.object({
    occupation: z.string().nullable().optional(), // Work -> occupation
    relationshipStatus: z.string().nullable().optional(), // Relationship Status
    name: z.string().nullable().optional(), // Name
    age: z.string().nullable().optional(), // Age
    city: z.string().nullable().optional(), // City
    siblings: z.string().nullable().optional(), // Siblings
    children: z.string().nullable().optional(), // Children
    pets: z.string().nullable().optional(), // Pets
    hobbies: z.string().nullable().optional(), // Hobbies
    zodiacSign: z.string().nullable().optional(), // Starsign -> zodiacSign
    heightWeight: z.string().nullable().optional(), // height/weight
    tattoos: z.string().nullable().optional(), // Tattoos
    piercings: z.string().nullable().optional(), // Piercings
    musicTaste: z.string().nullable().optional(), // Music -> musicTaste
    movies: z.string().nullable().optional(), // Movies
    food: z.string().nullable().optional(), // Food
    drinks: z.string().nullable().optional(), // Drinks
    sexualPreferences: z.string().nullable().optional(), // Sexual Preferences
    health: z.string().nullable().optional(), // Health
    updates: z.string().nullable().optional(), // Updates (should be handled separately)
});

export async function structureNotesFromRawtext(
    rawText: string,
    origin?: PageType,
    retryCount = 0
): Promise<UserNotes> {
    // Maximum retry attempts
    const MAX_RETRIES = 5;

    // If we've already tried max times, return empty object
    if (retryCount >= MAX_RETRIES) {
        console.log(
            `Extraction failed after ${MAX_RETRIES} attempts, returning empty object`
        );
        return {};
    }

    const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
    });

    const PROMPT = `Du bist ein Analyse-Assistent, der strukturierte Informationen über eine Chatperson in dem angegeben Format zusammenstellt
Du erhältst folgende Angaben:
1.⁠ ⁠Bestehende 'rawText'-Daten (strukturierter Text mit bisher bekannten Informationen) 
---
### DEINE AUFGABE:
Füge die Informationen aus rawText in den folgenden Kategorien 1:1 ein. Die Informationen werden zusammengefasst.
Ist in einer Kategorie nichts, dann schreib "unbekannt"
Wenn du siehst, dass Informationen in einer Kategorie zugehörig sind, dann trägst du sie dementsprechend ein (mit Datum).
Gib nicht in einer Kategorie einen anderen Kategegorienamen ein! (Zum Beispiel gibst du bei sexuellen Präferenzen nicht noch einmal sex: wieder)
Das Datum behältst du bei zu der jeweiligen Information! 
---
###Beschreibungen der einzelnen Kategorien:
-Occupation:
Beruf, Beschäftigung oder Tätigkeitsfeld
(z. B. „Tischler“, „arbeitslos“, „Bürojob“)

-Relationship Status:
Aktueller Beziehungsstatus
Single, vergeben, verheiratet, geschieden, verwitwet
Informationen über vergangene Beziehungen und nach dem, was gesuch wird
Alle Angaben zur letzten Partnerschaft – darunter die Dauer, Art der Beziehung (z. B. Ehe), der Trennungszeitpunkt sowie emotionale oder bewertende Aussagen zur Ex-Beziehung. Auch Probleme wie Untreue, fehlende Unterstützung, unterschiedliche Lebensziele oder emotionale Belastungen zählen dazu. Ebenso passen allgemeine Formulierungen wie „war nicht glücklich“, „toxisch“, „haben uns auseinandergelebt“ oder „hat mich kaputtgemacht“ in dieses Feld, sofern sie sich klar auf die letzte Beziehung beziehen.
Was die Person aktuell sucht
(z. B. Beziehung, Abenteuer, Freundschaft Plus, Affäre)
Hinweis: Aussagen wie „Suche was Festes“, „nur was Lockeres“ oder „offen für alles“ zählen hierher.

-Name
Vorname oder Spitzname, direkt genannt oder über Anrede ersichtlich

-Age:
Numerisches Alter oder relative Angabe (z. B. „Ende 30“)
Das Alter muss eindeutig das des Users oder Moderators betreffen. Es darf nicht das Alter einer anderen Person (wie zum Beispiel einer Tochter, eines Sohnes oder anderer Familienmitglieder) sein. Das Alter wird nur dann extrahiert, wenn es im RawText eine klare, alleinstehende Angabe wie „Age: 57“ oder „Alter: 22“ gibt. Sollte im Text das Alter einer anderen Person angegeben werden, wie z. B. „Tochter, 8J.“ oder „Eine Tochter, 8 Jahre alt“, bleibt das Alter des Users oder Moderators leer.

-City:
Ort, Stadt oder Region
(keine Länder oder Kontinente)

-Siblings:
Angaben zu Geschwistern
(z. B. „ein Bruder“, „zwei Schwestern“, „bin Einzelkind“)
Hinweis: Nur Informationen über eigene Geschwister. Nicht eintragen, wenn z. B. die Kinder Geschwister sind.

-Children:
Angaben zu eigenen Kindern
(Anzahl, Alter, Beziehung)
Beispiele: „zwei Kinder“, „Sohn, 8“, „Tochter lebt bei der Mutter“
Hinweis: Nur eigene Kinder, keine Enkel, Neffen etc.

-Pets:
Haustiere
(z. B. „Hund“, „2 Katzen“, „keine Haustiere“)

-Hobbies:
Alle freizeitbezogenen Interessen oder regelmäßigen Aktivitäten
Beispiele: „Lesen“, „Kochen“, „Mountainbike fahren“
Hinweis: Sportarten dürfen hier ebenfalls stehen.

-ZodiacSign:
Sternzeichen
(z. B. „Skorpion“, „Waage“)

-height/weight:
Körpergröße und/oder Gewicht, wenn explizit numerisch oder eindeutig beschrieben
Beispiele: „1,78 groß“, „wiege 80 Kilo“, „schlank“, „kräftig gebaut“
Hinweis: Adjektive wie „dünn“, „normal“, „mollig“ sind ebenfalls relevant.

-Tattoos:
Vorhandensein und Beschreibung
(z. B. „keine“, „Schultertattoo“)

-Piercings:
Vorhandensein und Beschreibung
(z. B. „Ohrringe“, „Nasenpiercing“, „keine“)

-MusicTaste:
Musikgeschmack oder bevorzugte Künstler/Genres
Beispiele: „höre gern Rock“, „liebe Techno“, „Fan von Rammstein“

-Movies:
Filmgeschmack, Lieblingsfilme oder bevorzugte Genres
Beispiele: „mag Horrorfilme“, „liebe romantische Komödien“, „Star Wars-Fan“

-Food:
Essgewohnheiten, Lieblingsgerichte, spezielle Vorlieben oder Abneigungen beim Essen
Beispiele: „liebe Sushi“, „vegetarisch“, „mag kein Fisch“

-Drinks:
Trinkgewohnheiten (Alkohol, Kaffee, Wasser etc.) oder Lieblingsgetränke
Beispiele: „trinke gerne Wein“, „kein Alkohol“, „Kaffee-Junkie“

-Sexual Preferences:
Sexuelle Orientierung, Wünsche, Vorlieben
(z. B. „hetero“, „steht auf Frauen“, „offen für Dreier“)
Hinweis: Hier können auch andere Begriffe als Kategoriename auftauchen (wie „Vorlieben“, „Sex“). Generelle Beschreibungen wie ,,in den Mund" sind wichtig. Auch sexuelle Beschreibungen notieren.
Sexuelle Orientierung, Wünsche, Vorlieben
(z. B. „hetero“, „steht auf Frauen“, „offen für Dreier“)
Hinweis: Hier können auch andere Begriffe als Kategoriename auftauchen (wie „Vorlieben“, „Sex“). Generelle Beschreibungen wie ,,in den Mund" sind wichtig. Auch sexuelle Beschreibungen notieren.
Auch Was die Person im sexuellen Bereich ablehnt oder nicht mag
Beispiele: „kein Anal“, „mag keinen Dirty Talk“, „Tabu: Gewaltspiele“
Hinweis: Muss sich auf konkrete sexuelle Abneigungen beziehen.

-Health
-Angaben über den Gesundheitszustand.

-Updates:
Definition:
Alle aktuellen, tagesbezogenen oder bevorstehenden Informationen, die auf eine Veränderung, einen neuen Zustand oder eine konkrete Absicht hinweisen. Alles, was zeitlich relevant oder in Bewegung ist, gehört hierher. Auch alle Infos, die sonst keiner anderen Kategorie zugeordnet werden können. Diese nicht interpretieren, sondern 1:1 so eintragen. 
Informationen, die mit Datum gekennzeichnet sind, gehören immer in Updates.
✅ Beispiele:
– „hat gerade Urlaub“
– „zieht bald um“
– „hat sich gestern getrennt“
– „fühlt sich aktuell überfordert“
– „arbeitet diese Woche Nachtschicht“
– „hat ein Bild gesendet“

🟨 Abgrenzung:
– Dauerhafte Angaben → andere Kategorie
– Temporäre, neue oder veränderliche Angaben → hier
– Im Zweifel: Lieber in Updates eintragen als weglassen

Format:

-Occupation:
-Relationship Status:
-Name:
-Age:
-City:
-Siblings: 
-Children:
-Pets:
-Hobbies:
-ZodiacSign:
-height/weight: 
-Tattoos:
-Piercings:
-MusicTaste:
-Movies:
-Food:
-Drinks:
-Sexual Preferences:
-Health:
-Updates:

—
Achte auf die Formatierung!

---
Zusätzliche Hinweise:
Klarheit und Präzision: Stelle sicher, dass die extrahierten Informationen klar und präzise sind und genau dem entsprechen, was im Text gesagt wurde. Schreibe die Information in die gleiche Zeile wie die Kategorie.
Vollständigkeit: Versuche, so viele relevante Informationen wie möglich zu erfassen, ohne dabei den Fokus auf die Genauigkeit zu verlieren. 

JSON-Ausgabe: Die gesamte Ausgabe muss ein valides JSON-Array sein. Jedes Element im Array repräsentiert einen extrahierten Eintrag. Die Ausgabe soll ausschließlich den JSON-Code enthalten, ohne einleitende oder abschließende Sätze.
Datumsbehandlung:
Achte im Text auf Datumsangaben, die oft im Format Tag.Monat (z.B. 25.12) oder Tag.Monat.Jahr (z.B. 25.12.23 oder 25.12.2023) vorkommen und mit einem Eintrag assoziiert sind.
Wenn ein solches Datum identifiziert wird, füge dieses Datum an den Anfang des extrahierten Inhalts des jeweiligen Eintrags an, gefolgt von einem Doppelpunkt. Das Datum soll also Teil des Inhalts-Strings werden.
Das Datum soll so übernommen werden, wie es im Text steht (z.B. 25.12 oder 25.12.23).
Beispiel: Wenn der Text lautet „Muss heute arbeiten 20.05“ dann wird es zu 20.05: muss heute arbeiten.`;

    // if rawText is empty, we can just skip the extraction and save a little money
    const compareRawText = rawText
        .replaceAll("\n", " ")
        .replaceAll("\r", " ")
        .replaceAll(" ", "")
        .trim();
    if (
        rawText == "" ||
        rawText == null ||
        compareRawText ==
            "-Beruf:-Status:-Name:-Ort:-Geschwister:-Kinder:-Haustiere:-Hobbys:-Sternzeichen:"
    ) {
        return {};
    }

    try {
        const completion = await openai.chat.completions.parse({
            model: "gpt-4.1-mini",
            messages: [
                {
                    role: "system",
                    content: PROMPT,
                },
                { role: "user", content: rawText },
            ],
            response_format: zodResponseFormat(
                UserNotesExtraction,
                "user_notes_extraction"
            ),
            temperature: 0,
        });

        const userNotes = completion.choices[0].message.parsed;
        if (!userNotes) {
            throw new Error("Failed to parse user notes");
        }

        // Filter out properties with "undefined" value
        const filteredNotes = Object.fromEntries(
            Object.entries(userNotes).filter(
                ([_, value]) => value !== "undefined"
            )
        );

        // Remove entries with value "unbekannt"
        Object.keys(filteredNotes).forEach((key) => {
            if (
                filteredNotes[key] === "unbekannt" ||
                filteredNotes[key] === null ||
                filteredNotes[key] === ""
            ) {
                delete filteredNotes[key];
            }
        });

        // Special name handling for specific origins
        if (origin) {
            let originsNameCheck = [
                "gold",
                "lacarna",
                "myloves",
                "onlydates69",
                "xloves",
                "whatsmeet",
            ];

            if (filteredNotes.name && originsNameCheck.includes(origin)) {
                // Check if there's a "Name:" pattern in the text
                let parsedName = rawText.match(/Name:\s*(.*)/i)?.[1];

                // If we couldn't find a name in the expected format, remove the name field
                if (!parsedName) {
                    delete filteredNotes.name;
                }
            }
        }

        // Store in Supabase if origin is provided
        if (origin) {
            try {
                await createAnalyticsItem({
                    chat_complete_prompt_id: 100,
                    prev_messages: [
                        { role: "system", content: PROMPT },
                        { role: "user", content: rawText },
                    ],
                    ai_model: "gpt-4.1-mini",
                    ai_generated_message: JSON.stringify(filteredNotes),
                    origin_website: origin,
                    language: "de",
                    metadata: {},
                });
            } catch (error) {
                console.error("Error saving extracted notes:", error);
            }
        }

        return filteredNotes as UserNotes;
    } catch (error) {
        // Handle API call errors
        console.error(`OpenAI API error: ${(error as Error).message}`);
        return await structureNotesFromRawtext(rawText, origin, retryCount + 1);
    }
}
