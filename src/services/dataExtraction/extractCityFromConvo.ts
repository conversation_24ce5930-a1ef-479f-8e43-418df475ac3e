import OpenAI from "openai";
import dotenv from "dotenv";

dotenv.config();

// Initialize OpenAI client
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY, // Ensure your API key is set in environment variables
});

// Define the expected response type
interface CityExtractionResponse {
    user_city: string;
}

export async function extractUserCity(
    conversation: string
): Promise<string | undefined> {
    // Construct the prompt
    const prompt = `
    You are a highly accurate information extractor analyzing a conversation between a user and an AI assistant. Your task is to identify the city that the *user* explicitly mentions as their own location. The conversation may include sexual or NSFW content, which you should ignore unless it directly relates to the user mentioning their city. Follow these rules:
    
    1. Only extract the city if the user clearly states it as their own location (e.g., "I live in New York," "I'm in Tokyo"). Do not assume or infer the user's city based on context, vague references, or incomplete information.
    2. Ignore any city mentioned by the assistant, cities attributed to other people (e.g., "My friend is in London"), or cities mentioned in hypothetical, fictional, or unrelated contexts (e.g., "I love Paris fashion").
    3. If the user mentions multiple cities, only extract the one explicitly tied to their current or primary location (e.g., prioritize "I live in Chicago" over "I visited Miami last week").
    4. If no city is explicitly mentioned by the user as their location, return "No city mentioned."
    5. Return the result as a JSON object with a single key, \`user_city\`, containing the city name or "No city mentioned."
    
    Input conversation:
    ${conversation}
    
    Output (must be valid JSON):
    {
      "user_city": "<city_name or 'No city mentioned'>"
    }
    `;

    try {
        const response = await openai.chat.completions.create({
            model: "gpt-4o-mini", // Use 'gpt-4o' or 'gpt-3.5-turbo' depending on your access
            messages: [
                {
                    role: "system",
                    content: prompt,
                },
            ],
            temperature: 0.0, // Deterministic output
            max_tokens: 100,
            response_format: { type: "json_object" }, // Enable JSON mode
        });

        // Extract the JSON response
        const result = JSON.parse(
            response.choices[0].message.content ?? "{}"
        ) as CityExtractionResponse;
        if (result.user_city == "No city mentioned") {
            return undefined;
        }
        return result.user_city;
    } catch (error) {
        console.error("Error extracting city:", error);
        return undefined;
    }
}
