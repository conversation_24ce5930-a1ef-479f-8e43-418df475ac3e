import dayjs from "dayjs";
import { SiteInfos } from "../../models/SiteInfos";

export function extractBirthday(
    userType: "customer" | "moderator",
    siteInfos: SiteInfos
): string | undefined {
    if (userType === "customer") {
        if (siteInfos.metaData.customerNotes?.age) {
            return `Dein Gesprächspartner ist ${siteInfos.metaData.customerNotes.age} Jahre alt.`;
        } else if (siteInfos.metaData.customerInfo.birthDate.date) {
            const date = siteInfos.metaData.customerInfo.birthDate.date;
            const birthDate = dayjs(date);
            const today = dayjs();
            const age = today.diff(birthDate, "year");

            // Calculate next birthday
            const nextBirthday = birthDate.year(today.year());
            if (nextBirthday.isBefore(today)) {
                nextBirthday.add(1, "year");
            }
            const daysUntilBirthday = nextBirthday.diff(today, "day");

            if (daysUntilBirthday > 0 && daysUntilBirthday <= 7) {
                return `Dein Gesprächspartner ist ${age} Jahre alt und hat in ${daysUntilBirthday} Tagen Geburtstag.`;
            } else if (daysUntilBirthday >= -7 && daysUntilBirthday < 0) {
                return `Dein Gesprächspartner ist ${age} Jahre alt und hatte vor ${Math.abs(
                    daysUntilBirthday
                )} Tagen Geburtstag.`;
            }

            return `Dein Gesprächspartner ist ${age} Jahre alt.`;
        } else if (siteInfos.metaData.customerInfo.birthDate.age) {
            return `Dein Gesprächspartner ist ${siteInfos.metaData.customerInfo.birthDate.age} Jahre alt.`;
        }
    } else {
        if (siteInfos.metaData.moderatorNotes?.age) {
            return `Du bist ${siteInfos.metaData.moderatorNotes.age} Jahre alt.`;
        } else if (siteInfos.metaData.moderatorInfo.birthDate.date) {
            const date = siteInfos.metaData.moderatorInfo.birthDate.date;
            const birthDate = dayjs(date);
            const today = dayjs();
            const age = today.diff(birthDate, "year");

            // Calculate next birthday
            const nextBirthday = birthDate.year(today.year());
            if (nextBirthday.isBefore(today)) {
                nextBirthday.add(1, "year");
            }
            const daysUntilBirthday = nextBirthday.diff(today, "day");

            if (daysUntilBirthday > 0 && daysUntilBirthday <= 7) {
                return `Du bist ${age} Jahre alt und hast in ${daysUntilBirthday} Tagen Geburtstag.`;
            } else if (daysUntilBirthday >= -7 && daysUntilBirthday < 0) {
                return `Du bist ${age} Jahre alt und hattest vor ${Math.abs(
                    daysUntilBirthday
                )} Tagen Geburtstag.`;
            } else {
                return `Du bist ${age} Jahre alt und dein Geburtstag ist am ${birthDate.format(
                    "DD.MM.YYYY"
                )}.`;
            }
        } else if (siteInfos.metaData.moderatorInfo.birthDate.age) {
            return `Du bist ${siteInfos.metaData.moderatorInfo.birthDate.age} Jahre alt.`;
        }
    }
}

export function extractModeratorAgeGenderTypingStyle(
    siteInfos: SiteInfos
): string | undefined {
    const notes = siteInfos.metaData.moderatorNotes;
    const info = siteInfos.metaData.moderatorInfo;

    let style = "Schreibe im Stil";

    // Determine gender part
    const gender = info.gender;
    if (gender) {
        style +=
            gender.toLowerCase() === "female" ? " einer Frau" : " eines Mannes";
    }

    // Add age if available, capped at 29
    const rawAge =
        notes?.age ||
        (info.birthDate.date
            ? dayjs().diff(dayjs(info.birthDate.date), "year")
            : info.birthDate.age);

    const age = rawAge ? Math.min(Number(rawAge), 29) : undefined;

    if (age) {
        style = style
            .replace(" Frau", ` ${age} Jahre alten Frau`)
            .replace(" Mannes", ` ${age} Jahre alten Mannes`);
    }

    return style;
}
