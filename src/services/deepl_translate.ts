import * as deepl from "deepl-node";
import OpenAI from "openai";

const translateDeepl = async (
    text: string,
    sourceLanguage: deepl.SourceLanguageCode,
    targetLanguage: deepl.TargetLanguageCode,
    glossaryId?: string
): Promise<string> => {
    const authKey = process.env.DEEPL_API_KEY!;
    const translator = new deepl.Translator(authKey);

    const options: deepl.TranslateTextOptions = {
        context: "",
        formality: "less",
        glossary: glossaryId,
    };

    const deeplResultText = await translator.translateText(
        text,
        sourceLanguage,
        targetLanguage,
        options
    );

    const systemPrompt =
        "You are a helpful assistant for the company Cloudworkers. Your task is to correct any spelling discrepancies in the transcribed text. Keep the language of the provided text. Make it sound more emotional based on the context and add emojis as you see fit. just don't overdo it. If you think the text is joyful, try to strech some works like heyyy or hiiii. Or missed youuu. Also try to remove most commas. If you notice anything explicit that is against the guidelines of openai, return `permission denied`.";

    const correctedText = await generateCorrectedTranslation(
        0,
        systemPrompt,
        deeplResultText.text
    )
        .then((correctedText) => {
            return correctedText;
        })
        .catch((error) => console.error(error));

    let outputText = deeplResultText.text;

    let permissionDeniedResponses = [
        "entschuldigung, aber ich kann",
        "permission denied",
    ];
    if (correctedText) {
        outputText = correctedText;
        permissionDeniedResponses.forEach((response) => {
            if (correctedText.toLowerCase().includes(response)) {
                outputText = deeplResultText.text;
            }
        });
    }

    return outputText;
};

export async function generateCorrectedTranslation(
    temperature: number,
    systemPrompt: string,
    transcript: string
) {
    // Configure the OpenAI API client
    const configuration = {
        apiKey: process.env.OPENAI_API_KEY,
    };
    const openai = new OpenAI(configuration);

    const completion = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        temperature: temperature,
        messages: [
            {
                role: "system",
                content: systemPrompt,
            },
            {
                role: "user",
                content: transcript,
            },
        ],
    });
    return completion.choices[0].message.content;
}

export { translateDeepl };
