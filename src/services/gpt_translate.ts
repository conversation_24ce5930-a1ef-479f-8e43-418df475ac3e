import OpenAI from "openai";
import { GPTAssistantPrompt, GPTTranslatePrompt } from "../util/gptPrompts";
import { GlossaryItem } from "../models/Glossary";
import { DeeplSupportedLanguage } from "../models/DeeplSupportedLanguages";

const translateGPT = async (
    text: string,
    sourceLanguage: DeeplSupportedLanguage,
    targetLanguage: DeeplSupportedLanguage,
    glossaryItems: Record<string, string>
): Promise<string | null> => {
    const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
    });

    const prompt = GPTAssistantPrompt(
        glossaryItems,
        sourceLanguage,
        targetLanguage
    );

    const translatePrompt = GPTTranslatePrompt(
        text,
        sourceLanguage,
        targetLanguage
    );

    const response = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
            {
                role: "assistant",
                content: prompt,
            },
            {
                role: "user",
                content: translatePrompt,
            },
        ],
        temperature: 0.55,
        max_tokens: 100,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
    });

    const resText = response.choices[0].message.content;
    if (
        !resText ||
        resText.toLowerCase().includes("permission denied") ||
        resText.toLowerCase().includes("entschuldigung, aber ich kann") ||
        resText.toLowerCase().includes("entschuldigung, das kann ich nicht") ||
        resText.toLowerCase().includes("verweigert") ||
        resText.toLowerCase().includes("es tut mir leid") ||
        resText
            .toLowerCase()
            .includes("entschuldigung, darauf kann ich nicht") ||
        resText.toLowerCase().includes("erlaubnis verweigert")
    ) {
        return null;
    }

    return resText
        .replace(`"`, "")
        .replace(`"`, "")
        .replace("{", "")
        .replace("}", "")
        .replace("[", "")
        .replace("]", "")
        .replace(`"`, "")
        .replace(`"`, "");
};

export { translateGPT };
