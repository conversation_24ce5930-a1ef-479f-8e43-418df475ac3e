// import { Message } from "../../models/SiteInfos";
// import { getPromptDB } from "../supabase/promptConfig";
// import { SiteInfos } from "../../models/SiteInfos";
// import { mergePromptWithObject } from "../inference/mergePrompt";
// import { AIAnalysisService } from "../inference/gemini";
// import { messagesToOpenAIMessages } from "../inference/messagesToOpenAIMessages";
// import OpenAI from "openai";
// import { createAnalyticsItem } from "../supabase/chatCompleteAnalyticsService";

// function parseTranslatedMessages(translatedText: string): Message[] {
//     if (!translatedText) return [];

//     const lines = translatedText
//         .split("\n")
//         .filter((line) => line.trim() !== "");
//     const messages: Message[] = [];

//     for (const line of lines) {
//         const trimmedLine = line.trim();

//         if (trimmedLine.startsWith("User:")) {
//             const text = trimmedLine.substring(5).trim(); // Remove 'User:' prefix
//             if (text) {
//                 messages.push({
//                     text,
//                     type: "received",
//                     messageType: "text",
//                 });
//             }
//         } else if (trimmedLine.startsWith("Model:")) {
//             const text = trimmedLine.substring(6).trim(); // Remove 'Model:' prefix
//             if (text) {
//                 messages.push({
//                     text,
//                     type: "sent",
//                     messageType: "text",
//                 });
//             }
//         }
//         // Ignore lines that don't start with 'User:' or 'Model:'
//     }

//     return messages;
// }

// export default async function translateMessages(
//     messages: Message[],
//     siteInfos: SiteInfos
// ): Promise<Message[]> {
//     const translateMessagesPromot = await getPromptDB(115);

//     if (!translateMessagesPromot) {
//         throw new Error("Translate messages prompt not found");
//     }

//     let extractedMetadata = {
//         moderatorGender: siteInfos.metaData.moderatorInfo.gender,
//         customerGender: siteInfos.metaData.customerInfo.gender,
//     };

//     const mergedPrompt = mergePromptWithObject(
//         translateMessagesPromot!.prompt,
//         extractedMetadata
//     );

//     const openaiMessages: OpenAI.ChatCompletionMessageParam[] =
//         await messagesToOpenAIMessages(siteInfos.origin, messages);

//     const response = await AIAnalysisService.generateTextFromPrompt(
//         mergedPrompt,
//         openaiMessages,
//         translateMessagesPromot!.ai_model_config.temperature,
//         translateMessagesPromot!.ai_model_config.top_p,
//         translateMessagesPromot!.ai_model_config.model
//     );

//     const translatedText = response.data?.generatedText;

//     await createAnalyticsItem(
//         {
//             chat_complete_prompt_id: 115,
//             prev_messages: openaiMessages,
//             origin_website: siteInfos.origin,
//             language: siteInfos.originLanguage ?? "DE",
//             ai_generated_message: translatedText ?? "",
//             ai_model: translateMessagesPromot!.ai_model_config.model,
//             metadata: siteInfos.metaData,
//             account_id: siteInfos.metaData.moderatorId,
//         },
//         siteInfos.metaData.moderatorId
//     );

//     if (!translatedText) {
//         throw new Error("No translation result received");
//     }

//     return parseTranslatedMessages(translatedText);
// }
