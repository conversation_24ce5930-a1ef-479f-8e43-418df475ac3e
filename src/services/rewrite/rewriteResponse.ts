import { Message, PageType, SiteInfos } from "../../models/SiteInfos";
import OpenAI from "openai";

import dotenv from "dotenv";
import { getPromptDB } from "../supabase/promptConfig";
import { mergePromptWithObject } from "../inference/mergePrompt";
import { createAnalyticsItem } from "../supabase/chatCompleteAnalyticsService";
import { AIAnalysisService, calculateCosts } from "../inference/gemini";

dotenv.config();

export default async function rewriteResponse(
    message: string,
    siteInfos: SiteInfos,
    origin: PageType
) {
    if (
        siteInfos.messages.length > 1 &&
        siteInfos.messages.filter((m) => m.type == "received").length <= 1
    ) {
        return message;
    }

    console.log(
        "Rewriting the message with the following attributes: ",
        siteInfos.metaData.rewriteConfig?.attributes
    );

    let rewriteConfig: any = {
        rewriteAge: siteInfos.metaData.rewriteConfig?.rewriteAge,
        rewriteGenderModerator: siteInfos.metaData.moderatorInfo.gender,
        rewriteGenderCostumer: siteInfos.metaData.customerInfo.gender,
        rewriteAdjectives:
            siteInfos.metaData.rewriteConfig?.attributes?.join(", "),
        minLength: siteInfos.metaData.minLength,
    };

    console.log("Rewrite config: ", rewriteConfig);

    const prompt = await getPromptDB(103);
    const promptInBG = await getPromptDB(97);

    let model:
        | "gemini-2.0-flash-001"
        | "gemini-2.5-flash"
        | "gemini-2.0-flash-lite-001" = prompt!.ai_model_config.model;

    let modelBG = promptInBG!.ai_model_config.model;

    const lastCustomerMessage = siteInfos.messages
        .filter((m) => m.type == "received")
        .at(-1);

    if (
        siteInfos.metaData.ins &&
        siteInfos.metaData.ins < 5 &&
        lastCustomerMessage &&
        lastCustomerMessage.text &&
        lastCustomerMessage.text.length < 60
    ) {
        model = "gemini-2.5-flash";
        rewriteConfig = {
            ...rewriteConfig,
            extraRuleForShortText:
                "-Kürze die Nachricht, aber halte eine Mindestlänge von 60 Zeichen ein. Schreibe MAXIMAL 120 Zeichen!",
        };
    }
    prompt!.prompt = prompt!.prompt.replace(
        "{{previousMessages}}",
        messagesToSystemMessageString(siteInfos.messages)
    );
    const mergedPrompt = mergePromptWithObject(prompt!.prompt, rewriteConfig);

    promptInBG!.prompt = promptInBG!.prompt.replace(
        "{{previousMessages}}",
        messagesToSystemMessageString(siteInfos.messages)
    );
    const mergedPromptBG = mergePromptWithObject(
        promptInBG!.prompt,
        rewriteConfig
    );

    const messages: OpenAI.ChatCompletionMessageParam[] = [
        {
            role: "system",
            content: mergedPrompt,
        },
        {
            role: "user",
            content: message,
        },
    ];

    const messagesBG: OpenAI.ChatCompletionMessageParam[] = [
        {
            role: "system",
            content: mergedPromptBG,
        },
        {
            role: "user",
            content: message,
        },
    ];

    const response = await AIAnalysisService.generateTextFromPrompt(
        mergedPrompt,
        messages,
        prompt!.ai_model_config.temperature,
        prompt!.ai_model_config.top_p,
        model,
        false
    );

    const responseBG = await AIAnalysisService.generateTextFromPrompt(
        mergedPromptBG,
        messagesBG,
        promptInBG!.ai_model_config.temperature,
        promptInBG!.ai_model_config.top_p,
        modelBG,
        false
    );

    if (!response || !response.data) {
        console.error("Error rewriting response: ", response);
        return message;
    }

    // @ts-ignore
    console.log("Rewritten Response: ", response?.data.generatedText);

    const cost = calculateCosts(model, response?.data?.usage);
    const costBG = calculateCosts(modelBG, responseBG?.data?.usage);

    const analyticsMessages = siteInfos.messages.map(
        (m) => m.type + ": " + m.text
    );

    await createAnalyticsItem({
        chat_complete_prompt_id: 97,
        prev_messages: messagesBG,
        origin_website: origin,
        language: "de",
        // @ts-ignore,
        ai_generated_message: responseBG?.data.generatedText ?? "",
        ai_model: modelBG,
        metadata: {
            ...siteInfos.metaData,
            cost: costBG,
            messages: analyticsMessages,
        },
        account_id: siteInfos.accountId,
        // @ts-ignore
        corrected_output: responseBG?.data.generatedText ?? "",
    });

    await createAnalyticsItem({
        chat_complete_prompt_id: 103,
        prev_messages: messages,
        origin_website: origin,
        language: "de",
        // @ts-ignore
        ai_generated_message: response?.data.generatedText ?? "",
        ai_model: model,
        metadata: {
            ...siteInfos.metaData,
            cost,
            messages: analyticsMessages,
        },
        account_id: siteInfos.accountId,
        // @ts-ignore
        corrected_output: response?.data.generatedText ?? "",
    });

    if (!response || !response.success) {
        return message;
    }

    return response?.data?.generatedText ?? message;
}

function messagesToSystemMessageString(openaiMessages: Message[]) {
    const lastFourMessages = openaiMessages.slice(-4);

    return lastFourMessages
        .map((m, i) => {
            const isNewest = i == lastFourMessages.length - 1;
            const isOldest = i == 0;

            const messageInfo = m.type == "received" ? "User:" : "Moderator:";

            return `<Nachricht ${i + 1}>${
                isOldest ? " (älteste)" : isNewest ? " (neueste)" : ""
            }: ${messageInfo} ${m.text}</Nachricht ${i + 1}>`;
        })
        .join("\n");
}
