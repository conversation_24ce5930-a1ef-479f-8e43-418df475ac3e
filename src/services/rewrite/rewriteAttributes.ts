import { PageType } from "../../models/SiteInfos";
import supabaseAdminClient from "../../util/createClient";

export interface ModeratorRewriteInfos {
    age: number;
    gender: "male" | "female";
}

export async function getRewrittenAttributes(
    origin: PageType,
    moderatorRewriteInfos: ModeratorRewriteInfos,
    roomId?: string
): Promise<string[]> {
    // If no roomId, just create random adjectives without database operations
    if (!roomId) {
        return createNewAdjectives(origin);
    }

    // Try to get existing adjectives
    const { data: existingAdjectives, error } = await supabaseAdminClient()
        .from("rewrite_adjectives")
        .select("adjectives")
        .eq("room_id", roomId)
        .eq("origin", origin)
        .single();

    // If adjectives exist, return them
    if (existingAdjectives && !error) {
        return existingAdjectives.adjectives;
    }

    // If no adjectives exist, create new ones
    const newAdjectives = createNewAdjectives(origin);

    // Save the new adjectives to the database
    const { data: savedAdjectives, error: saveError } =
        await supabaseAdminClient()
            .from("rewrite_adjectives")
            .insert({
                room_id: roomId,
                origin: origin,
                adjectives: newAdjectives,
            })
            .select("adjectives")
            .single();

    if (saveError) {
        throw new Error(`Failed to save adjectives: ${saveError.message}`);
    }
    return savedAdjectives.adjectives;
}

function createNewAdjectives(origin: PageType): string[] {
    // First adjective: random from intelligence array
    let intelligenceAdjectives = ["dumm", "hirnlos", "unintelligent"];

    if (origin == "justlo") {
        return [
            "ehrlich",
            "humorvoll",
            "ehrgeizig",
            "zielstrebig",
            "hilfsbereit",
            "geduldig",
            "offen",
            "verantwortungsbewusst",
            "loyal",
        ];
    }

    const firstAdjective =
        intelligenceAdjectives[
            Math.floor(Math.random() * intelligenceAdjectives.length)
        ];

    // Second adjective: random from directness array
    let directnessAdjectives = [
        "deutlich",
        "direkt",
        "explizit",
        "konkret",
        "klar",
        "ohne Umschweife",
        "plakativ",
        "unmissverständlich",
        "unverblümt",
    ];

    //@ts-ignore
    if (origin == "justlo") {
        // Add opposite adjectives from the original array
        directnessAdjectives = [
            "indirekt",
            "verschwommen",
            "unklar",
            "vage",
            "umschweifend",
            "zurückhaltend",
            "verblümt",
            "zweideutig",
            "fokussiert",
            "tiefsinning",
        ];
    }

    const secondAdjective =
        directnessAdjectives[
            Math.floor(Math.random() * directnessAdjectives.length)
        ];

    // Third adjective: always "freundlich"
    const thirdAdjective = "freundlich";

    return [firstAdjective, secondAdjective, thirdAdjective];
}

// Test function - remove this in production
function testAdjectiveGeneration() {
    console.log("Testing adjective generation with 100 tries:");
    console.log("=".repeat(50));

    for (let i = 0; i < 100; i++) {
        // Generate random ages across different ranges
        const ages = [20, 25, 30, 35, 40, 45, 50]; // Mix of young, middle, mature
        const randomAge = ages[Math.floor(Math.random() * ages.length)];

        const testInfo: ModeratorRewriteInfos = {
            age: randomAge,
            gender: Math.random() > 0.5 ? "male" : "female",
        };

        const adjectives = createNewAdjectives("test");
        const ageGroup =
            randomAge < 25 ? "young" : randomAge <= 40 ? "middle" : "mature";

        console.log(
            `Try ${i + 1}: Age ${randomAge} (${ageGroup}) -> [${adjectives.join(
                ", "
            )}]`
        );
    }
}

// Uncomment to run the test
// testAdjectiveGeneration();

async function testRewriteAttributes() {
    const testInfo: ModeratorRewriteInfos = {
        age: 20,
        gender: "male",
    };
    const adjectives = await getRewrittenAttributes("test", testInfo, "123");
    console.log(adjectives);
}

// testRewriteAttributes();
