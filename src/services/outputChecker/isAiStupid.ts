import OpenAI from "openai";
import { messagesToOpenAIMessages } from "../inference/messagesToOpenAIMessages";
import { getPromptDB } from "../supabase/promptConfig";
import {
    AnalyticsItem,
    ExtractedMetadata,
    SiteInfos,
} from "../../models/SiteInfos";
import { mergePrompt } from "../inference/mergePrompt";
import { createAnalyticsItem } from "../supabase/chatCompleteAnalyticsService";

export async function isAIStupid(
    resText: string,
    siteInfos: SiteInfos,
    extractedMetadata: ExtractedMetadata
) {
    const prompt = await getPromptDB(30);

    const mergedPrompt = mergePrompt(prompt!, extractedMetadata);

    let newMessages = await messagesToOpenAIMessages(
        siteInfos.origin,
        siteInfos.messages
    );

    newMessages = newMessages.slice(-4);

    if (!prompt) {
        console.error("Prompt to check messages not found");
        return false;
    }

    const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
    });

    newMessages = [
        {
            role: "system",
            content: mergedPrompt!,
        },
        ...newMessages,
        {
            role: "assistant",
            content: resText,
        },
    ];

    const response = await openai.chat.completions.create({
        ...prompt.ai_model_config,
        messages: newMessages,
    });

    const output = response.choices[0].message.content;

    const analyticsItem: AnalyticsItem = {
        chat_complete_prompt_id: prompt.id!,
        prev_messages: newMessages,
        origin_website: siteInfos.origin,
        language: "DE",
        ai_generated_message: output!,
        ai_model: prompt.ai_model_config.ai_model,
        metadata: {},
    };

    await createAnalyticsItem(analyticsItem);

    if (resText == "JA") {
        return true;
    }

    return false;
}
