import OpenAI from "openai";
import { getPromptDB } from "../supabase/promptConfig";
import { sendLogSnag } from "../logsnag";
import { messagesToOpenAIMessages } from "../inference/messagesToOpenAIMessages";
import { AnalyticsItem, Message, PageType } from "../../models/SiteInfos";
import { createAnalyticsItem } from "../supabase/chatCompleteAnalyticsService";

export async function doesCustomerWantPhoto(
    messages: Message[],
    origin: PageType
) {
    const prompt = await getPromptDB(9);

    if (!prompt) {
        console.error("Prompt to check messages not found");
        return false;
    }

    const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
    });

    let newMessages = [
        {
            role: "system",
            content: prompt.prompt,
        },
        ...(await messagesToOpenAIMessages(origin, messages)).slice(-5, -1),
    ];

    // Check if the last user message contains specific words
    const lastUserMessage = newMessages
        .reverse()
        .find((message) => message.role === "user");

    if (lastUserMessage && lastUserMessage.content) {
        let keywords: string[] = [];
        if (origin == "b3" || origin == "love-room" || origin == "wifu") {
            keywords = ["photo", "foto", "bild", "schick", "zeig", "sehen"];
        }
        const containsKeyword = keywords.some((keyword) =>
            lastUserMessage
                .content!.toString()
                .toLowerCase()
                .includes(keyword.toLowerCase())
        );

        if (containsKeyword) {
            return true;
        }
    }

    const response = await openai.chat.completions.create({
        ...prompt.ai_model_config,
        messages: newMessages,
    });

    const resText = response.choices[0].message.content;
    const analyticsItem: AnalyticsItem = {
        chat_complete_prompt_id: prompt.id!,
        prev_messages: newMessages,
        origin_website: origin,
        language: "DE",
        ai_generated_message: resText!,
        ai_model: prompt.ai_model_config.ai_model,
        metadata: {},
    };

    await createAnalyticsItem(analyticsItem);

    if (resText == "JA") {
        await sendLogSnag(
            "photo_request_output_checker",
            undefined,
            "Output is invalid"
        );
        return true;
    }

    return false;
}
