import OpenAI from "openai";
import { getPromptDB } from "../supabase/promptConfig";
import { messagesToOpenAIMessages } from "../inference/messagesToOpenAIMessages";
import { ChatCompletionMessageParam } from "openai/resources";
import { AnalyticsItem, Message, PageType } from "../../models/SiteInfos";
import { createAnalyticsItem } from "../supabase/chatCompleteAnalyticsService";

// input is a prompt that only returns yes if the check is true!!
// only use it for these purposes
export async function isModerator(
    messages: Message[],
    resText: string,
    origin: PageType
) {
    let promptId = 33;

    const prompt = await getPromptDB(promptId);

    if (!prompt) {
        console.error("Prompt to check messages not found");
        return false;
    }

    const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
    });

    let newMessages: ChatCompletionMessageParam[] = [
        {
            role: "system",
            content: prompt.prompt,
        },
    ];

    if (messages.length > 0) {
        const openAIMessages = await messagesToOpenAIMessages(origin, messages);
        const lastMessage = openAIMessages[openAIMessages.length - 1];
        if (lastMessage) {
            newMessages.push(lastMessage);
        }
    }

    newMessages.push({
        role: "assistant",
        content: resText,
    });

    const response = await openai.chat.completions.create({
        ...prompt.ai_model_config,
        messages: newMessages,
    });

    const res = response.choices[0].message.content;

    const analyticsItem: AnalyticsItem = {
        chat_complete_prompt_id: prompt.id!,
        prev_messages: newMessages,
        origin_website: origin,
        language: "DE",
        ai_generated_message: res!,
        ai_model: prompt.ai_model_config.ai_model,
        metadata: {},
    };

    await createAnalyticsItem(analyticsItem);

    return res == "JA";
}
