import OpenAI from "openai";
import { getPromptDB } from "../supabase/promptConfig";
import { sendLogSnag } from "../logsnag";
import { AnalyticsItem, SiteInfos } from "../../models/SiteInfos";
import { createAnalyticsItem } from "../supabase/chatCompleteAnalyticsService";

// input is a prompt that only returns yes if the check is true!!
// only use it for these purposes
export async function isGenderCorrect(message: string, siteInfos: SiteInfos) {
    if (siteInfos.origin == "avz") return true;

    let promptId = 23; // default is 23. Mod Fm Cus M

    // if (genderRoles == "femaleCustomer-maleMod") promptId = 24;
    // else if (genderRoles == "maleCustomer-maleMod") promptId = 25;
    // else if (genderRoles == "femaleCustomer-femaleMod") promptId = 26;

    if (
        siteInfos.metaData.customerInfo.gender == "female" &&
        siteInfos.metaData.moderatorInfo.gender == "male"
    )
        promptId = 24;
    else if (
        siteInfos.metaData.customerInfo.gender == "male" &&
        siteInfos.metaData.moderatorInfo.gender == "male"
    )
        promptId = 25;
    else if (
        siteInfos.metaData.customerInfo.gender == "female" &&
        siteInfos.metaData.moderatorInfo.gender == "female"
    )
        promptId = 26;

    const prompt = await getPromptDB(promptId);

    if (!prompt) {
        console.error("Prompt to check messages not found");
        return false;
    }

    const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
    });

    const messages = [
        {
            role: "system",
            content: prompt.prompt,
        },
        {
            role: "assistant",
            content: message,
        },
    ];

    const response = await openai.chat.completions.create({
        ...prompt.ai_model_config,
        messages,
    });

    const resText = response.choices[0].message.content;

    const analyticsItem: AnalyticsItem = {
        chat_complete_prompt_id: prompt.id!,
        prev_messages: messages,
        origin_website: siteInfos.origin,
        language: "DE",
        ai_generated_message: resText!,
        ai_model: prompt.ai_model_config.ai_model,
        metadata: {},
    };

    await createAnalyticsItem(analyticsItem);

    if (resText == "NEIN") {
        console.log(
            `# Output is invalid - genderRole ${siteInfos.metaData.customerInfo.gender}-${siteInfos.metaData.moderatorInfo.gender}`
        );
        return false;
    }

    return true;
}
