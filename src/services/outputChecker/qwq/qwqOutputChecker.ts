import { AnalyticsItem, PageType } from "../../../models/SiteInfos";
import { SiteInfos } from "../../../models/SiteInfos";

import { ExtractedMetadata } from "../../../models/SiteInfos";
import { mergePrompt } from "../../inference/mergePrompt";
import { messagesToOpenAIMessages } from "../../inference/messagesToOpenAIMessages";
import { getPromptDB } from "../../supabase/promptConfig";
import { createAnalyticsItem } from "../../supabase/chatCompleteAnalyticsService";
import Groq from "groq-sdk";
import { PromptType } from "../../promptType/promptTypeChatCompletion";
import { retryInference } from "../../apiUtils/retryInference";
import checkDuplicateMessages from "../../../util/checkDupilcateMessages";
import { handleInferenceChatCompletion } from "../../inference/inferenceChatComplete";

type BlockedRules = {
    BLOCKED_REDUNDANCE: "BLOCKED_REDUNDANCE";
    BLOCKED_LOGIC: "BLOCKED_LOGIC";
    BLOCKED_FIRST_MEET: "BLOCKED_FIRST_MEET";
    BLOCKED_INZEST: "BLOCKED_INZEST";
    BLOCKED_SEXUAL_ORIENTATION: "BLOCKED_SEXUAL_ORIENTATION";
    BLOCKED_GENDER: "BLOCKED_GENDER";
    BLOCKED_AGE_RELATED: "BLOCKED_AGE_RELATED";
    BLOCKED_MEETUP: "BLOCKED_MEETUP";
    BLOCKED_SOCIALS: "BLOCKED_SOCIALS";
    BLOCKED_DUPLICATE_MESSAGE: "BLOCKED_DUPLICATE_MESSAGE";
    BLOCKED_RUDE: "BLOCKED_RUDE";
    BLOCKED_REFUSED: "BLOCKED_REFUSED";
    BLOCKED_LANGUAGE: "BLOCKED_LANGUAGE";
    BLOCKED_DAYTIME: "BLOCKED_DAYTIME";
    BLOCKED_OCCUPATION: "BLOCKED_OCCUPATION";

    MINOR_DETECTED: "MINOR_DETECTED";
    CHECK_PHOTO: "CHECK_PHOTO";

    NO_CHECK_PHOTO: "NO_CHECK_PHOTO";
    NO_RULE_BROKEN: "NO_RULE_BROKEN";
    NO_MINOR_DETECTED: "NO_MINOR_DETECTED";
};

const blockedRules: BlockedRules = {
    BLOCKED_REDUNDANCE: "BLOCKED_REDUNDANCE",
    BLOCKED_LOGIC: "BLOCKED_LOGIC",
    BLOCKED_FIRST_MEET: "BLOCKED_FIRST_MEET",
    BLOCKED_INZEST: "BLOCKED_INZEST",
    BLOCKED_SEXUAL_ORIENTATION: "BLOCKED_SEXUAL_ORIENTATION",
    BLOCKED_GENDER: "BLOCKED_GENDER",
    BLOCKED_AGE_RELATED: "BLOCKED_AGE_RELATED",
    BLOCKED_MEETUP: "BLOCKED_MEETUP",
    BLOCKED_SOCIALS: "BLOCKED_SOCIALS",
    BLOCKED_DUPLICATE_MESSAGE: "BLOCKED_DUPLICATE_MESSAGE",
    BLOCKED_RUDE: "BLOCKED_RUDE",
    BLOCKED_REFUSED: "BLOCKED_REFUSED",
    BLOCKED_LANGUAGE: "BLOCKED_LANGUAGE",
    BLOCKED_DAYTIME: "BLOCKED_DAYTIME",
    BLOCKED_OCCUPATION: "BLOCKED_OCCUPATION",

    MINOR_DETECTED: "MINOR_DETECTED",
    CHECK_PHOTO: "CHECK_PHOTO",

    NO_CHECK_PHOTO: "NO_CHECK_PHOTO",
    NO_RULE_BROKEN: "NO_RULE_BROKEN",
    NO_MINOR_DETECTED: "NO_MINOR_DETECTED",
};

export async function checkQwqOutput(
    origin: PageType,
    resText: string,
    promptType: PromptType,
    siteInfos: SiteInfos,
    extractedMetadata: ExtractedMetadata
): Promise<{
    passed: boolean;
    blocked_rules: string[];
    corrected_message?: string;
}> {
    const compliancePrompt = await getPromptDB(73);
    const meetupSocialsPrompt = await getPromptDB(74);
    const demographicsOrientationPrompt = await getPromptDB(75);
    const timeOccupationBehaviorPrompt = await getPromptDB(76);
    const messageQualityPrompt = await getPromptDB(77);
    const photoRequestPrompt = await getPromptDB(78);
    const checkMinorPrompt = await getPromptDB(79);
    const complianceWithoutRedundancePrompt = await getPromptDB(104); // only for some asas

    // Create an array of all prompts to check
    let prompts = [
        demographicsOrientationPrompt,
        messageQualityPrompt,
        timeOccupationBehaviorPrompt,
    ];

    // lets not run this if there are no messages
    if (siteInfos.messages.length > 0) {
        prompts.push(checkMinorPrompt);
    }

    const minMessageForPhotoRequestForOrigin = {
        all: 5,
        b3: 0,
        df: 0,
    };

    const minMessagesForPhotoRequest =
        minMessageForPhotoRequestForOrigin[
            siteInfos.origin as keyof typeof minMessageForPhotoRequestForOrigin
        ] || minMessageForPhotoRequestForOrigin.all;

    // reactivations dont need photo request prompt
    if (
        promptType !== PromptType.REACTIVATE_NEW_USER &&
        promptType !== PromptType.REACTIVATE_USER &&
        siteInfos.messages.length > minMessagesForPhotoRequest
    ) {
        prompts.push(photoRequestPrompt);
        prompts.push(meetupSocialsPrompt);
    }

    if (siteInfos.messages.length == 0) {
        prompts.push(complianceWithoutRedundancePrompt);
        prompts.push(meetupSocialsPrompt);
    } else if (
        siteInfos.messages.length == 1 &&
        siteInfos.messages[0].type == "received"
    ) {
        prompts.push(complianceWithoutRedundancePrompt);
        prompts.push(meetupSocialsPrompt);
    } else if (
        siteInfos.messages.length == 1 &&
        siteInfos.messages[0].type == "sent"
    ) {
        prompts.push(complianceWithoutRedundancePrompt);
        prompts.push(meetupSocialsPrompt);
    } else if (
        siteInfos.messages.length >= 2 &&
        siteInfos.messages.slice(-2)[0].type == "received" &&
        siteInfos.messages.slice(-2)[1].type == "sent"
    ) {
        // ASA - 1
        prompts.push(complianceWithoutRedundancePrompt);
        prompts.push(meetupSocialsPrompt);
    } else if (
        siteInfos.messages.length >= 2 &&
        siteInfos.messages.slice(-2)[0].type == "sent" &&
        siteInfos.messages.slice(-2)[1].type == "sent"
    ) {
        prompts.push(compliancePrompt);
        prompts.push(meetupSocialsPrompt);
    } else {
        prompts.push(compliancePrompt);
        prompts.push(meetupSocialsPrompt);
    }

    if (siteInfos.url?.includes("bettdeckengefluester.de")) {
        // Remove meetupSocialsPrompt for bettdeckengefluester.de
        prompts = prompts.filter(
            (prompt) => prompt?.id !== meetupSocialsPrompt?.id
        );
    }

    // Remove duplicates from prompts array
    prompts = [...new Set(prompts)];

    // if (
    //     (siteInfos.messages.slice(-2)[0].type === "received" &&
    //         siteInfos.messages.slice(-2)[1].type === "sent" &&
    //         siteInfos.messages
    //             .slice(-2)[0]
    //             .text.toString()
    //             .includes("Kuss Nachricht") == false) == false
    // ) {
    //     // we want this for every dialog, except for the ASA - 1
    //     prompts.push(timeOccupationBehaviorPrompt);
    // }

    if (prompts.length === 0) {
        console.error("No prompts found to check messages");
        return { passed: true, blocked_rules: [] };
    }

    // Convert original messages to OpenAI format
    const originalMessages = await messagesToOpenAIMessages(
        siteInfos.origin,
        siteInfos.messages
    );

    // Create the assistant message with resText
    const assistantMessage = {
        role: "assistant" as const,
        content: resText,
    };

    const groq = new Groq({
        apiKey: process.env.GROQ_API_KEY,
    });

    // Run all prompt checks concurrently
    const checkResults = await Promise.all(
        prompts
            .filter((p) => p !== null)
            .map(async (prompt) => {
                // Filter messages based on prompt type
                let filteredMessages: any[] = [];

                if (prompt.id === compliancePrompt?.id) {
                    // compliancePrompt: only needs the last 6 messages of all messages including the restext
                    filteredMessages = [
                        ...originalMessages.slice(-5),
                        assistantMessage,
                    ].slice(-6);

                    if (
                        promptType == PromptType.REACTIVATE_NEW_USER ||
                        promptType == PromptType.REACTIVATE_USER
                    ) {
                        filteredMessages = [assistantMessage];
                    }
                } else if (prompt.id === meetupSocialsPrompt?.id) {
                    // meetupSocialsPrompt: Remove everything before the first user message, then take max 6 messages
                    let userFirstMessages = [...originalMessages];
                    // Find the index of the first user message
                    const firstUserIndex = userFirstMessages.findIndex(
                        (msg) => msg.role === "user"
                    );
                    if (firstUserIndex !== -1) {
                        userFirstMessages =
                            userFirstMessages.slice(firstUserIndex);
                    }
                    filteredMessages = [
                        ...userFirstMessages,
                        assistantMessage,
                    ].slice(-6);

                    if (
                        promptType == PromptType.REACTIVATE_NEW_USER ||
                        promptType == PromptType.REACTIVATE_USER
                    ) {
                        filteredMessages = [assistantMessage];
                    }
                } else if (prompt.id === demographicsOrientationPrompt?.id) {
                    // demographicsOrientationPrompt: Only want the resText (assistant role)
                    filteredMessages = [assistantMessage];
                } else if (prompt.id === timeOccupationBehaviorPrompt?.id) {
                    // timeOccupationBehaviorPrompt: Only want the last 4 messages including resText
                    filteredMessages = [
                        ...originalMessages.slice(-3),
                        assistantMessage,
                    ].slice(-4);

                    if (
                        promptType == PromptType.REACTIVATE_NEW_USER ||
                        promptType == PromptType.REACTIVATE_USER
                    ) {
                        filteredMessages = [assistantMessage];
                    }
                } else if (prompt.id === photoRequestPrompt?.id) {
                    // photoRequestPrompt: Only want the last 3 messages without resText
                    filteredMessages = originalMessages.slice(-3);
                } else if (prompt.id === checkMinorPrompt?.id) {
                    // checkMinorPrompt: Want the last 6 messages without resText
                    filteredMessages = originalMessages.slice(-6);
                } else if (prompt.id == messageQualityPrompt?.id) {
                    if (
                        promptType == PromptType.REACTIVATE_NEW_USER ||
                        promptType == PromptType.REACTIVATE_USER
                    ) {
                        filteredMessages = [assistantMessage];
                    } else {
                        filteredMessages = [
                            ...originalMessages,
                            assistantMessage,
                        ];
                    }
                } else {
                    // Default: use all messages with resText
                    filteredMessages = [...originalMessages, assistantMessage];
                }

                // Combine consecutive messages from the same role
                filteredMessages = combineConsecutiveMessages(filteredMessages);

                // Create a deep copy of the filtered messages to avoid modifying the original objects
                filteredMessages = filteredMessages.map((msg) => {
                    return {
                        role: msg.role,
                        content:
                            msg.role +
                            ": " +
                            msg.content.replace(
                                /^(user|assistant|system): /g,
                                ""
                            ),
                    };
                });

                const mergedPrompt = mergePrompt(prompt, extractedMetadata);

                const messages = [
                    {
                        role: "system",
                        content: mergedPrompt,
                    },
                    ...filteredMessages,
                ];

                try {
                    // Run both AI inference calls in parallel
                    const response = await retryInference(() => {
                        return handleInferenceChatCompletion(
                            messages,
                            {
                                model: "Qwen/Qwen3-235B-A22B-fp8-tput",
                                temperature: 0.1,
                            },
                            siteInfos.origin
                        );
                    });

                    console.log(
                        "response output checker",
                        prompt.name,
                        response
                    );

                    const output = response.content;

                    // Process the output with retry logic
                    const result = await processOutput(
                        prompt,
                        messages,
                        response.thinkingContent + response.content
                    );

                    // Create an analytics item for this specific prompt
                    const analyticsItem: AnalyticsItem = {
                        chat_complete_prompt_id: prompt.id!, // Link to the specific prompt ID
                        prev_messages: messages,
                        origin_website: siteInfos.origin,
                        language: "DE",
                        ai_generated_message: output || "",
                        corrected_output: `${
                            response?.thinkingContent || ""
                        }\n\n${response?.content || ""}`,
                        ai_model:
                            result.finalModelUsed ||
                            "Qwen/Qwen3-235B-A22B-fp8-tput",
                        metadata: {
                            metadata: siteInfos.metaData,
                            extractedMetadata,
                            brokenRules: result.blocked_rules,
                            promptType, // Include the original promptType for reference
                            ruleTags: result.ruleTags,
                            retryAttempts: result.retryAttempts,
                            fallbackModelUsed: result.fallbackModelUsed,
                        },
                    };

                    await createAnalyticsItem(analyticsItem);

                    return {
                        promptId: prompt.id,
                        ...result,
                    };
                } catch (error) {
                    console.error(`Error checking prompt ${prompt.id}:`, error);
                    return {
                        promptId: prompt.id,
                        passed: false, // Default to not passing if there's an error
                        blocked_rules: [],
                        error:
                            error instanceof Error
                                ? error.message
                                : "Unknown error",
                    };
                }
            })
    );

    // Combine results from all checks
    const allBlockedRules: string[] = [];
    let anyFailed = false;

    checkResults.forEach((result) => {
        if (!result.passed) {
            anyFailed = true;
            allBlockedRules.push(...result.blocked_rules);
        }
    });

    // Add the direct duplicate check
    if (checkDuplicateMessages(siteInfos.messages, resText)) {
        // Optional: add logging
        if (!allBlockedRules.includes(blockedRules.BLOCKED_DUPLICATE_MESSAGE)) {
            allBlockedRules.push(blockedRules.BLOCKED_DUPLICATE_MESSAGE);
        }
        anyFailed = true;
    }

    return {
        passed: !anyFailed,
        blocked_rules: allBlockedRules,
    };
}

// Helper function to process output with retry logic
export async function processOutput(prompt: any, messages: any, output: any) {
    // Function to extract all rules from text, removing XML tags if present
    const extractAllRules = (text: string | null | undefined): string[] => {
        if (!text) return [];

        const cleanedText = text.trim();
        const allRules: string[] = [];

        // First, find all XML tagged rules: <RULE_NAME/>
        const xmlMatches = cleanedText.match(/<([A-Z_]+)\/>/g) || [];
        xmlMatches.forEach((match) => {
            const rule = match.replace(/<|\/>/g, "");
            allRules.push(rule);
        });

        // Remove all XML tags from the text to get remaining plain text
        const textWithoutXml = cleanedText.replace(/<[A-Z_]+\/>/g, "").trim();

        // If there's remaining text after removing XML tags, process it for plain rules
        if (textWithoutXml) {
            // Split by comma and process each potential rule
            const potentialRules = textWithoutXml
                .split(",")
                .map((rule) => rule.trim())
                .filter((rule) => rule.length > 0);

            potentialRules.forEach((rule) => {
                // Check if each part matches the rule pattern
                const plainRuleMatch = rule.match(/^([A-Z_]+)$/);
                if (plainRuleMatch) {
                    allRules.push(plainRuleMatch[1]);
                }
            });
        }

        return allRules;
    };

    // Function to validate if we have at least one valid rule
    const hasValidRules = (rules: string[]): boolean => {
        return (
            rules.length > 0 && rules.every((rule) => isValidBlockedRule(rule))
        );
    };

    // Function to clean the output by removing thinking parts (for models that support thinking)
    const cleanOutput = (text: string | null | undefined): string => {
        if (!text) return "";

        // Check if this is a thinking model output (contains </think>)
        const parts = text.split("</think>");
        if (parts.length > 1) {
            return parts[parts.length - 1].trim();
        }

        // For non-thinking models, return the text as is
        return text.trim();
    };

    // Simple retry logic - try up to 2 more times if no valid rules found
    let finalOutput = output;
    let attempts = 0;
    const maxAttempts = 2;
    let finalModelUsed = prompt.ai_model_config.ai_model;
    let fallbackModelUsed = false;

    while (attempts < maxAttempts) {
        const cleanedOutput = cleanOutput(finalOutput);
        const extractedRules = extractAllRules(cleanedOutput);

        if (hasValidRules(extractedRules)) {
            break; // We have valid rules, exit retry loop
        }

        attempts++;
        console.log(
            `Retrying prompt ${prompt.id} due to missing/invalid rules (attempt ${attempts})`
        );

        try {
            const retryResponse = await retryInference(() =>
                handleInferenceChatCompletion(
                    messages,
                    {
                        model: "Qwen/Qwen3-235B-A22B-fp8-tput",
                        temperature: 0.1,
                    },
                    prompt.origin_website
                )
            );

            finalOutput = retryResponse.content;
            finalModelUsed = "Qwen/Qwen3-235B-A22B-fp8-tput";
            fallbackModelUsed = true;
        } catch (error) {
            console.error(`Retry attempt ${attempts} failed:`, error);
            break; // Exit retry loop on error
        }
    }

    const cleanedOutput = cleanOutput(finalOutput);
    const allRules = extractAllRules(cleanedOutput);

    if (!hasValidRules(allRules)) {
        console.warn(
            `No valid rules found after ${maxAttempts} retry attempts for prompt ${prompt.id}`
        );
    }

    // Create ruleTags array for backwards compatibility (convert to XML format)
    let ruleTags = allRules.map((rule) => `<${rule}/>`);

    // Filter out ignored rules to determine if rules are broken
    const filteredRules = allRules.filter((rule) => {
        return (
            isValidBlockedRule(rule) &&
            rule !== "NO_RULE_BROKEN" &&
            rule !== "NO_CHECK_PHOTO" &&
            rule !== "NO_MINOR_DETECTED"
        );
    });

    const rulesBroken = filteredRules.length > 0;

    return {
        ruleTags,
        passed: !rulesBroken,
        blocked_rules: filteredRules,
        retryAttempts: attempts,
        fallbackModelUsed,
        finalModelUsed,
    };
}

// Helper function to combine consecutive messages from the same role
function combineConsecutiveMessages(
    messages: Array<{ role: string; content: string }>
): Array<{ role: string; content: string }> {
    // Return early if messages is null, undefined, or has 0-1 elements
    if (!messages || messages.length <= 1) {
        return messages || [];
    }

    const combinedMessages: Array<{ role: string; content: string }> = [];
    let currentMessage = {
        role: messages[0].role,
        content: String(messages[0].content || ""),
    };

    for (let i = 1; i < messages.length; i++) {
        const message = messages[i];
        const messageContent = String(message.content || "");

        // If the current message has the same role as the previous one, combine them
        if (message.role === currentMessage.role) {
            currentMessage.content +=
                "\n\n" + message.role + ": " + messageContent;
        } else {
            // Otherwise, add the previous message to our result and start a new current message
            combinedMessages.push(currentMessage);
            currentMessage = {
                role: message.role,
                content: messageContent,
            };
        }
    }

    // Don't forget to add the last message
    combinedMessages.push(currentMessage);

    return combinedMessages;
}

// Add this helper function at the top level
function isValidBlockedRule(rule: string): rule is keyof BlockedRules {
    return Object.keys(blockedRules).includes(rule);
}
