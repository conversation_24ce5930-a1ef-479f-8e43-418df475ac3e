/**
 * Test file to debug the rule tag regex matching
 */

// Function to clean the output by removing thinking parts (copied from your code)
function cleanOutput(text: string | null | undefined): string {
    if (!text) return "";

    // Extract the part after </think> if it exists
    const thinkMatch = text.match(/\<\/think\>([\s\S]*)$/);
    if (thinkMatch && thinkMatch[1]) {
        return thinkMatch[1].trim();
    }

    return text.trim();
}

// Test function to process a string and check rule tag extraction
function testRuleTagExtraction(inputString: string) {
    // Clean the output
    const cleanedOutput = cleanOutput(inputString);

    // Check if the output contains NO_RULE_BROKEN tag
    const hasNoRuleBroken = /<NO_RULE_BROKEN\/>/.test(cleanedOutput);

    // Extract all rule tags using the original regex
    const originalRegex = /<([A-Z_]+)\/>/g;
    const ruleTags = cleanedOutput.match(originalRegex) || [];

    // Let's try an alternative regex that might be more precise
    const alternativeRegex = /<([A-Z_]+)\/>/g;
    const ruleTagsAlt = [...cleanedOutput.matchAll(alternativeRegex)].map(
        (match) => match[0]
    );

    // Filter out the tags we want to ignore
    const filteredRuleTags = ruleTags.filter(
        (tag) =>
            tag !== "<NO_RULE_BROKEN/>" &&
            tag !== "<NO_CHECK_PHOTO/>" &&
            tag !== "<NO_MINOR_DETECTED/>"
    );

    // Convert tags to rule names
    const brokenRules = filteredRuleTags.map((tag) =>
        tag.replace(/<|\/>/g, "")
    );

    return {
        hasNoRuleBroken,
        ruleTags,
        filteredRuleTags,
        brokenRules,
    };
}

// Test with your example
const testString = `<think>
Okay, let me check the user's message and the assistant's response against the given rules.

First, looking at the user's message: They mention that Wednesday night is bad because they have to clean up early Thursday morning, so any other time is fine. The assistant's response questions why they need to stay up all night and suggests they take it slow. 

Checking for BLOCKED_DUPLICATE_MESSAGE: The assistant's response doesn't seem to repeat any previous messages. Each exchange here is progressing the conversation, so no duplication.

Next, BLOCKED_RUDE: The assistant's reply is playful but not rude or offensive. They're clarifying the user's intentions and suggesting a slower pace, which isn't dismissive or insulting. 

No rules are broken here. The conversation continues in a consensual and playful manner without violating the specified rules.
</think>

<NO_RULE_BROKEN/>`;

testRuleTagExtraction(testString);

// Let's also test with a modified version that has the rule name in a different format in the thinking section
const testStringModified = `<think>
Okay, let's go through each rule one by one to see if the assistant's message breaks any of them.

First, BLOCKED_REDUNDANCE: No issues here.
Next, BLOCKED_LOGIC: No issues here.
BLOCKED_FIRST_MEET: No issues here.
<BLOCKED_FIRST_MEET> This shouldn't be matched in the thinking section.

So none of the rules are broken. The assistant's message is okay.
</think>

<NO_RULE_BROKEN/>`;

testRuleTagExtraction(testStringModified);
