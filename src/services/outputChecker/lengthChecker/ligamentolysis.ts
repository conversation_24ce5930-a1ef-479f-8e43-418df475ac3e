// Only extend length if text delta is 30 characters
// regenerate max 2 times with the generated text if it does not reach the threshold
// if it does not reach the threshold after 2 regenerations, return undefined to regenerate the resText

import { Groq } from "groq-sdk";
import { AnalyticsItem, PageType } from "../../../models/SiteInfos";
import lengthChecker from "./lengthChecker";
import { getPromptDB } from "../../supabase/promptConfig";
import { createAnalyticsItem } from "../../supabase/chatCompleteAnalyticsService";
import { retryGroqCall } from "../../apiUtils/retryInference";

export default async function ligamentolysis(
    text: string,
    minLength: number,
    page: PageType
) {
    const ligamentolysisPrompt = await getPromptDB(80)!;

    // Add whitespace before emojis if there are none
    function addWhitespaceBeforeEmojis(text?: string): string {
        // Regex to match emojis that don't have whitespace before them
        const emojiRegex =
            /(?<!\s)([\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}])/gu;
        return text?.replace(emojiRegex, " $1") || "";
    }

    // Add whitespace after punctuation marks if there are none
    function addWhitespaceAfterPunctuation(text?: string): string {
        // Regex to match ., ?, ! that don't have whitespace after them (but aren't at end of string)
        const punctuationRegex = /([.?!])(?!\s|$)/g;
        return text?.replace(punctuationRegex, "$1 ") || "";
    }

    // Apply preprocessing: emoji whitespace and punctuation whitespace
    let currentText = addWhitespaceBeforeEmojis(text);
    currentText = addWhitespaceAfterPunctuation(currentText);

    const textLength = lengthChecker(currentText, minLength, page);
    const textDelta = minLength - textLength;

    // Only proceed if text is significantly shorter than required

    // Try to extend the text up to 2 times
    let attempts = 0;
    const maxAttempts = 2;

    while (
        lengthChecker(currentText, minLength, page) < 0 &&
        attempts < maxAttempts
    ) {
        attempts++;

        try {
            const groq = new Groq({
                apiKey: process.env.GROQ_API_KEY,
            });

            const messages = [
                {
                    role: "system",
                    content: ligamentolysisPrompt!.prompt,
                },
                {
                    role: "user",
                    content: currentText,
                },
            ];

            const chatCompletion = await retryGroqCall<any>(() =>
                groq.chat.completions.create({
                    ...ligamentolysisPrompt!.ai_model_config,
                    messages: messages,
                })
            );

            const result = chatCompletion.choices[0]?.message?.content;

            // Create an analytics item for this specific prompt
            const analyticsItem: AnalyticsItem = {
                chat_complete_prompt_id: ligamentolysisPrompt!.id!, // Link to the specific prompt ID
                prev_messages: messages,
                origin_website: page,
                language: "DE",
                ai_generated_message: result || "",
                ai_model: ligamentolysisPrompt!.ai_model_config.ai_model,
                metadata: {
                    text,
                    minLength,
                    page,
                },
            };

            await createAnalyticsItem(analyticsItem);

            if (result) {
                currentText = result.includes("\n")
                    ? result.split("\n")[0]
                    : result;
            } else {
                break; // Exit if no result was returned
            }
        } catch (error) {
            console.error("Error extending text with Groq:", error);
            break;
        }
    }

    const lengthDelta = lengthChecker(currentText, minLength, page);
    // If we still haven't reached minimum length after max attempts, return undefined
    if (lengthDelta < 0) {
        console.log(
            "Ligamentolysis: Text is still too short. returning undefined",
            lengthDelta
        );
        return undefined;
    } else {
        console.log(
            "Ligamentolysis: Text is now long enough. returning text",
            lengthDelta
        );
        return currentText;
    }
}
