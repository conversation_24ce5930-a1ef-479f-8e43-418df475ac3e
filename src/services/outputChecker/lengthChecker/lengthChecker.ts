import { PageType } from "../../../models/SiteInfos";

export default function lengthChecker(
    text: string,
    minLength: number,
    page: PageType
): number {
    console.log("text lengthChecker", text);
    const RULES: Record<
        PageType,
        {
            countWhiteSpaces: boolean;
            countSpecialCharacters: boolean;
            countEmojis: boolean;
            countEmojisTwice?: boolean;
        }
    > = {
        cw: {
            countWhiteSpaces: false,
            countSpecialCharacters: false,
            countEmojis: false,
        },
        gold: {
            countWhiteSpaces: false,
            countSpecialCharacters: false,
            countEmojis: true,
        },
        avz: {
            countWhiteSpaces: false,
            countSpecialCharacters: false,
            countEmojis: true,
        },
        whatsmeet: {
            countWhiteSpaces: false,
            countSpecialCharacters: false,
            countEmojis: true,
        },
        xloves: {
            countWhiteSpaces: false,
            countSpecialCharacters: false,
            countEmojis: true,
        },
        onlydates69: {
            countWhiteSpaces: false,
            countSpecialCharacters: false,
            countEmojis: true,
        },
        myloves: {
            countWhiteSpaces: false,
            countSpecialCharacters: false,
            countEmojis: true,
        },
        lacarna: {
            countWhiteSpaces: false,
            countSpecialCharacters: false,
            countEmojis: true,
        },
        b3: {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: true,
        },
        wifu: {
            countWhiteSpaces: false,
            countSpecialCharacters: false,
            countEmojis: false,
        },
        df: {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: true,
        },
        fpc: {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: false,
        },
        flirtking: {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: false,
        },
        teddy: {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: true,
        },
        kizzle: {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: true,
        },
        "love-room": {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: true,
        },
        route66: {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: true,
        },
        livecreator: {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: true,
        },
        pankek: {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: true,
        },
        translate: {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: true,
        },
        cherry: {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: true,
        },
        test: {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: true,
        },
        torchwood: {
            countWhiteSpaces: false,
            countSpecialCharacters: true,
            countEmojis: true,
            countEmojisTwice: true,
        },
        "single-jungle": {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: true,
        },
        justlo: {
            countWhiteSpaces: true,
            countSpecialCharacters: true,
            countEmojis: true,
        },
    };

    const cleanTextToCount = (text: string) => {
        let processedText = text;
        let extraCounter = 0;

        if (page == "torchwood") {
            extraCounter -= 5;
        }

        // If we should NOT count whitespaces, remove them
        if (!RULES[page].countWhiteSpaces) {
            processedText = processedText.replace(/\s/g, "");
        }

        // If we should NOT count special characters, remove them
        if (!RULES[page].countSpecialCharacters) {
            // This regex keeps only alphanumeric and whitespace
            processedText = processedText.replace(/[^\w\s]/g, "");
        }

        // If we should NOT count emojis, remove them
        // Note: This needs a more specific regex for emojis as the current one
        // overlaps with special character handling
        if (!RULES[page].countEmojis && RULES[page].countSpecialCharacters) {
            // This is a simplified approach - a dedicated emoji regex would be better
            const emojiRegex =
                /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu;
            processedText = processedText.replace(emojiRegex, "");
        }

        return { text: processedText, extraCount: extraCounter };
    };

    const result = cleanTextToCount(text);
    console.log("result lengthChecker", result);
    const textLength = result.text.trim().length + result.extraCount;

    console.log("textLength", textLength);

    const deltaTextLength = textLength - minLength;

    return deltaTextLength;
}
