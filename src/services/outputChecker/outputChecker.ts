import OpenAI from "openai";
import Prompt from "../../models/Pompt";
import { getPromptDB } from "../supabase/promptConfig";
import { sendLogSnag } from "../logsnag";
import { messagesToOpenAIMessages } from "../inference/messagesToOpenAIMessages";
import { AnalyticsItem, Message, PageType } from "../../models/SiteInfos";
import { createAnalyticsItem } from "../supabase/chatCompleteAnalyticsService";

// input is a prompt that only returns yes if the check is true!!
// only use it for these purposes
export async function isOutputValid(
    messages: Message[],
    resText: string,
    origin: PageType
) {
    // meetup prompt
    const prompt = await getPromptDB(8); // meetup prompt

    if (!prompt) {
        return false;
    }

    const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
    });

    let newMessages = [
        {
            role: "system",
            content: prompt.prompt,
        },
    ];

    if (messages.length > 0) {
        let lastFourMessages = await messagesToOpenAIMessages(origin, messages);
        lastFourMessages = lastFourMessages.slice(-4);
        //@ts-ignore
        newMessages.push(...lastFourMessages);
    }

    newMessages.push({
        role: "assistant",
        content: resText,
    });

    const response = await openai.chat.completions.create({
        ...prompt.ai_model_config,
        messages: newMessages,
    });

    const res = response.choices[0].message.content;

    const analyticsItem: AnalyticsItem = {
        chat_complete_prompt_id: prompt.id!,
        prev_messages: newMessages,
        origin_website: origin,
        language: "DE",
        ai_generated_message: res!,
        ai_model: prompt.ai_model_config.ai_model,
        metadata: {},
    };

    await createAnalyticsItem(analyticsItem);

    if (res == "JA") {
        await sendLogSnag("gpt_output_checker", undefined, "Output is valid");
        return false;
    }

    return true;
}
