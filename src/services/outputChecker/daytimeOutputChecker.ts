import OpenAI from "openai";
import dayjs from "dayjs";
import "dayjs/locale/de";
import localizedFormat from "dayjs/plugin/localizedFormat";
import { getPromptDB } from "../supabase/promptConfig";
import { sendLogSnag } from "../logsnag";
import {
    AnalyticsItem,
    ComputedMetadata,
    PageType,
} from "../../models/SiteInfos";
import { createAnalyticsItem } from "../supabase/chatCompleteAnalyticsService";

export async function isDayTimeCorrect(
    message: string,
    origin: PageType,
    computedMetadata: ComputedMetadata
) {
    const prompt = await getPromptDB(27);

    if (!prompt) {
        console.error("Prompt to check messages not found");
        return false;
    }

    const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
    });

    // Set locale to German
    dayjs.locale("de");
    dayjs.extend(localizedFormat);

    let copyPrompt = prompt.prompt.replaceAll(
        "{{day}}",
        computedMetadata.currentDayTime!.day
    );
    copyPrompt = copyPrompt.replaceAll(
        "{{promptDay}}",
        computedMetadata.currentDayTime!.promptDay
    );
    copyPrompt = copyPrompt.replaceAll(
        "{{time}}",
        computedMetadata.currentDayTime!.time
    );
    copyPrompt = copyPrompt.replaceAll(
        "{{dayTime}}",
        computedMetadata.currentDayTime!.dayTime
    );

    const messages = [
        {
            role: "system",
            content: copyPrompt,
        },
        {
            role: "assistant",
            content: message,
        },
    ];

    const response = await openai.chat.completions.create({
        ...prompt.ai_model_config,
        messages,
    });

    const resText = response.choices[0].message.content;

    const analyticsItem: AnalyticsItem = {
        chat_complete_prompt_id: prompt.id!,
        prev_messages: messages,
        origin_website: origin,
        language: "DE",
        ai_generated_message: resText!,
        ai_model: prompt.ai_model_config.ai_model,
        metadata: {},
    };

    await createAnalyticsItem(analyticsItem);

    if (resText == "JA") {
        await sendLogSnag(
            "daytime_request_output_checker",
            undefined,
            "Output is invalid"
        );
        return false;
    }

    return true;
}
