import { PageType } from "../../models/SiteInfos";
import { handleInferenceChatCompletion } from "../inference/inferenceChatComplete";
import { createAnalyticsItem } from "../supabase/chatCompleteAnalyticsService";
import { getPromptDB } from "../supabase/promptConfig";

export async function isGerman(text: string, origin: PageType) {
    console.log("German checker...");
    const prompt = await getPromptDB(110);
    console.log("prompt", prompt?.ai_model_config);
    const response = await handleInferenceChatCompletion(
        [
            {
                role: "system",
                content: prompt?.prompt,
            },
            {
                role: "user",
                content: text,
            },
        ],
        prompt?.ai_model_config,
        origin
    );

    if (!response) {
        return false;
    }

    const { content } = response;

    await createAnalyticsItem(
        {
            chat_complete_prompt_id: 110,
            prev_messages: [
                {
                    role: "system",
                    content: prompt?.prompt,
                },
                {
                    role: "user",
                    content: text,
                },
            ],
            origin_website: origin,
            language: "en",
            // @ts-ignore
            ai_generated_message: content,
            ai_model: prompt?.ai_model_config.model,
            metadata: {
                temperature: prompt?.ai_model_config?.temperature || 0.7,
                top_p: prompt?.ai_model_config?.top_p || 0.9,
                model: prompt?.ai_model_config?.model,
            },
        },
        ""
    );

    if (content?.toLowerCase() == "yes") {
        return true;
    }

    return false;
}
