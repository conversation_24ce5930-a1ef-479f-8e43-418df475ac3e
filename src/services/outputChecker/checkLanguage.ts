export async function isLanguageGerman(text: string, maxRetries = 5) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const initialResponse = await fetch(
                "https://onursarikaya2000-facebook-fasttext-language-iden-3d3ffe1.hf.space/call/predict",
                {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: process.env.HUGGINGFACE_API_KEY ?? "",
                    },
                    method: "POST",
                    body: JSON.stringify({ data: [text] }),
                }
            );

            const res = await initialResponse.json();

            const finalResponse = await fetch(
                `https://onursarikaya2000-facebook-fasttext-language-iden-3d3ffe1.hf.space/call/predict/${res.event_id}`,
                {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: process.env.HUGGINGFACE_API_KEY ?? "",
                    },
                    method: "GET",
                }
            );

            if (!finalResponse.body) {
                throw new Error("No response body");
            }

            const reader = finalResponse.body.getReader();
            let result = "";
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                result += new TextDecoder().decode(value);
            }

            const parsedResult = JSON.parse(
                result.split("\n")[1].split("data: ")[1]
            );
            const firstPrediction = parsedResult[0];
            const output = {
                predictedLanguage: firstPrediction.label,
                confidence: firstPrediction.confidences[0].confidence,
            };

            console.log(
                "Language detection result:",
                JSON.stringify(output, null, 2)
            );
            return output.predictedLanguage === "deu_Latn";
        } catch (error) {
            console.error(`Attempt ${attempt} failed:`, error);
            if (attempt === maxRetries) {
                return false;
            }
            // Wait for a short time before retrying (optional)
            await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
        }
    }
}
