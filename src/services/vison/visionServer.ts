import {
    ImageAnalysisRequest,
    ImageAnalysisResponse,
    ImageAnalysisData,
} from "../../models/SiteInfos";

import dotenv from "dotenv";
import axios from "axios";
dotenv.config();

const API_URL = process.env.VISION_API_URL + "/api/image/";
const API_KEY = process.env.VISION_API_KEY;

// Configure axios with timeout and retry
const axiosConfig = {
    timeout: 120000, // 60 seconds timeout
    headers: {
        "Content-Type": "application/json",
        "x-api-key": API_KEY,
    },
};

// Health check function to test API connectivity
export async function checkVisionAPIHealth(): Promise<boolean> {
    try {
        console.log(`Checking vision API health at: ${API_URL}`);

        // Try a simple request to see if the service is reachable
        // You might want to implement a specific health endpoint on the vision API
        const response = await axios.get(
            API_URL.replace("/api/image/", "/health"),
            {
                timeout: 10000, // Shorter timeout for health check
                headers: {
                    "x-api-key": API_KEY,
                },
            }
        );

        console.log(`Vision API health check response: ${response.status}`);
        return response.status === 200;
    } catch (error) {
        console.error("Vision API health check failed:", {
            message: error instanceof Error ? error.message : "Unknown error",
            code: axios.isAxiosError(error) ? error.code : undefined,
            status: axios.isAxiosError(error)
                ? error.response?.status
                : undefined,
        });
        return false;
    }
}

export async function analyzeImage(
    imageAnalysisRequest: ImageAnalysisRequest
): Promise<ImageAnalysisResponse> {
    try {
        console.log(`Making vision API request to: ${API_URL}analyze`);
        console.log(`Request payload:`, {
            imageUrls: imageAnalysisRequest.imageUrls?.length || 0,
            origin: imageAnalysisRequest.origin,
            type: imageAnalysisRequest.type,
        });

        // Call the API endpoint
        const response = await axios.post(
            API_URL + "analyze",
            {
                imageUrls: imageAnalysisRequest.imageUrls,
                origin: imageAnalysisRequest.origin,
                type: imageAnalysisRequest.type,
            },
            axiosConfig
        );

        console.log(`Vision API response status: ${response.status}`);
        return response.data;
    } catch (error) {
        if (axios.isAxiosError(error)) {
            console.error("Vision API Error Details:", {
                status: error.response?.status,
                statusText: error.response?.statusText,
                url: error.config?.url,
                method: error.config?.method,
                message: error.message,
                responseData: error.response?.data,
                imageUrls: imageAnalysisRequest.imageUrls,
            });

            // Specific handling for different error types
            if (error.response?.status === 502) {
                throw new Error(
                    `Vision API service unavailable (502): The external vision service is currently down or unreachable`
                );
            } else if (error.response?.status === 429) {
                throw new Error(
                    `Vision API rate limit exceeded (429): Please try again later`
                );
            } else if (
                error.response?.status === 401 ||
                error.response?.status === 403
            ) {
                throw new Error(
                    `Vision API authentication failed (${error.response.status}): Check API key configuration`
                );
            } else if (error.code === "ECONNABORTED") {
                throw new Error(
                    `Vision API timeout: Request took longer than 60 seconds`
                );
            }
        }

        console.error("Unexpected error in analyzeImage:", error);
        throw new Error(
            `Vision API request failed: ${
                error instanceof Error ? error.message : "Unknown error"
            }`
        );
    }
}

interface FindAssetRequest {
    origin: string;
    searchQuery: string;
    moderatorId?: string;
    userId?: string;
    chatId?: string;
    messages: Array<{
        imageSrc?: string;
        role: string;
        content: string;
    }>;
    privateGallery?: string[];
}

export async function findAsset(
    request: FindAssetRequest
): Promise<ImageAnalysisData[]> {
    try {
        console.log(`Making findAsset API request to: ${API_URL}findasset`);
        console.log(`Request payload:`, {
            origin: request.origin,
            searchQuery: request.searchQuery,
            moderatorId: request.moderatorId,
            userId: request.userId,
            chatId: request.chatId,
            messagesCount: request.messages?.length || 0,
            privateGalleryCount: request.privateGallery?.length || 0,
        });

        const response = await axios.post(
            API_URL + "findasset",
            {
                origin: request.origin,
                searchQuery: request.searchQuery,
                moderatorId: request.moderatorId,
                userId: request.userId,
                messages: request.messages,
                privateGallery: request.privateGallery,
            },
            axiosConfig
        );

        console.log("findAsset response status:", response.status);
        console.log("findAsset response data structure:", {
            hasData: !!response.data?.data,
            dataType: typeof response.data?.data,
            isArray: Array.isArray(response.data?.data),
            dataLength: Array.isArray(response.data?.data)
                ? response.data.data.length
                : "N/A",
        });

        // Ensure response.data.data is an array before filtering
        const dataToFilter = Array.isArray(response.data?.data)
            ? response.data.data
            : [];

        console.log("filteredData length:", dataToFilter.length);

        return dataToFilter;
    } catch (error) {
        if (axios.isAxiosError(error)) {
            console.error("FindAsset API Error Details:", {
                status: error.response?.status,
                statusText: error.response?.statusText,
                url: error.config?.url,
                method: error.config?.method,
                message: error.message,
                responseData: error.response?.data,
            });

            // Specific handling for different error types
            if (error.response?.status === 502) {
                throw new Error(
                    `FindAsset API service unavailable (502): The external vision service is currently down or unreachable`
                );
            } else if (error.response?.status === 429) {
                throw new Error(
                    `FindAsset API rate limit exceeded (429): Please try again later`
                );
            } else if (
                error.response?.status === 401 ||
                error.response?.status === 403
            ) {
                throw new Error(
                    `FindAsset API authentication failed (${error.response.status}): Check API key configuration`
                );
            } else if (error.code === "ECONNABORTED") {
                throw new Error(
                    `FindAsset API timeout: Request took longer than 30 seconds`
                );
            }
        }

        console.error("Unexpected error in findAsset:", error);
        throw new Error(
            `FindAsset API request failed: ${
                error instanceof Error ? error.message : "Unknown error"
            }`
        );
    }
}
