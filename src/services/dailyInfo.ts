import DailyInfo from "../models/DailyInfo";

export async function getDailyInfo(
    long: number,
    lat: number,
    country: string = "DE"
): Promise<DailyInfo | undefined> {
    try {
        const url = new URL(
            process.env.DAILY_INFO_URL! + "/api/current-events"
        );
        url.searchParams.append("country", country);
        url.searchParams.append("lat", lat.toString());
        url.searchParams.append("long", long.toString());

        const response = await fetch(url);

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error fetching daily info", error);
        return undefined;
    }
}
