import { DeeplSupportedLanguage } from "../models/DeeplSupportedLanguages";

const dataSet = {
    EN: ["doggystyle", "naughty"],
};

// pronounciation for whisper per language
export default function pronounciationWhisperSourceLang(
    sourceLanguage: DeeplSupportedLanguage
) {
    if (Object.keys(dataSet).includes(sourceLanguage)) {
        // @ts-ignore
        return dataSet[sourceLanguage];
    }
    return undefined;
}
