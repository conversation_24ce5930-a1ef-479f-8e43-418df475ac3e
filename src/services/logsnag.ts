import { LogSnag } from "@logsnag/node";
import { PageType } from "../models/SiteInfos";

const logsnag = new LogSnag({
    token: process.env.LOGSNAG_API_TOKEN!,
    project: "colombia",
});

export async function sendLogSnag(
    channel:
        | "image-detected"
        | "choked"
        | "alert"
        | "openai_error"
        | "system"
        | "blocked_phrases"
        | "gpt_output_checker"
        | "photo_request_output_checker"
        | "daytime_request_output_checker"
        | "togetherai_error",
    origin?: PageType,
    message?: string,
    description?: string
) {
    const events = {
        "image-detected": "IMAGE DETECTED ON " + origin?.toUpperCase(),
        choked: "SYSTEM CHOKED",
        alert: "ALERT ON " + origin?.toUpperCase(),
        openai_error: "OPENAI ERROR " + origin?.toUpperCase(),
        system: "SYSTEM ERROR " + message,
        blocked_phrases:
            "BLOCKED PHRASES DETECTED ON " +
            origin?.toUpperCase() +
            " - " +
            message,
        gpt_output_checker:
            "GPT OUTPUT CHECKER " + origin?.toUpperCase() + " - " + message,
        photo_request_output_checker:
            "PHOTO REQUEST OUTPUT CHECKER " +
            origin?.toUpperCase() +
            " - " +
            message,
        daytime_request_output_checker:
            "DAYTIME REQUEST OUTPUT CHECKER " +
            origin?.toUpperCase() +
            " - " +
            message,
        togetherai_error:
            "TOGETHERAI ERROR " + origin?.toUpperCase() + " - " + message,
    };

    const icons = {
        "image-detected": "📸",
        choked: "🛑",
        alert: "⚠️",
        openai_error: "🤦🏻‍♂️",
        system: "👾",
        blocked_phrases: "🚨",
        gpt_output_checker: "🤖",
        photo_request_output_checker: "🤖",
        daytime_request_output_checker: "🤖",
        togetherai_error: "🤖",
    };

    const event = {
        channel: `${channel}-${origin}`,
        event: events[channel],
        icon: icons[channel],
        notify: true,
        description,
    };

    // Track an event
    await logsnag.track(event);
}
