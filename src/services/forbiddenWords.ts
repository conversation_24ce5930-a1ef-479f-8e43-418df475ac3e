/**"default",
  "default_moderator_male_user_male",
  "default_moderator_female_user_female",
  "moderator_male",
  "no_previous_messages",
  "klaps",
  "kiss",
  "reactivate_user",
  "outputControl",
  "gptControl",
  "summary" */

import { SiteInfos } from "../models/SiteInfos";

export default function checkForBlockedPhrases(
    input: string,
    siteInfos: SiteInfos
): false | string {
    const createRegex = (phrase: string) => {
        // Handle phrases that start or end with spaces specially
        const trimmedPhrase = phrase.trim();
        const startsWithSpace = phrase.startsWith(" ");
        const endsWithSpace = phrase.endsWith(" ");

        if (startsWithSpace && endsWithSpace) {
            // Phrase like " abo " - needs space before and after
            return new RegExp(
                `\\s+${trimmedPhrase.replace(
                    /[.*+?^${}()|[\]\\]/g,
                    "\\$&"
                )}\\s+`,
                "i"
            );
        } else if (startsWithSpace) {
            // Phrase like " abo" - needs space before
            return new RegExp(
                `\\s+${trimmedPhrase.replace(
                    /[.*+?^${}()|[\]\\]/g,
                    "\\$&"
                )}\\b`,
                "i"
            );
        } else if (endsWithSpace) {
            // Phrase like "abo " - needs space after
            return new RegExp(
                `\\b${trimmedPhrase.replace(
                    /[.*+?^${}()|[\]\\]/g,
                    "\\$&"
                )}\\s+`,
                "i"
            );
        } else {
            // Regular phrase handling (existing logic)
            const words = phrase
                .split(" ")
                .filter((word) => word.length > 0) // Filter out empty strings
                .map((word) => word.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"));
            return new RegExp(words.join("(?:\\s+\\w+){0,3}\\s*"), "i");
        }
    };

    /**
     * The regex pattern created is special because it:
     * Escapes special regex characters in the phrase
     * Allows up to 3 words between parts of the phrase using (?:\\s+\\w+){0,3}\\s*
     * For example, if searching for "hello world":
     * Would match "hello world"
     * Would match "hello beautiful amazing world"
     * Would not match "hello wonderful amazing great world" (too many words in between)
     */
    const checkForbiddenWords = (
        text: string,
        phrases: string[]
    ): false | string => {
        const detectedPhrases = phrases.filter((phrase) =>
            createRegex(phrase.toLowerCase()).test(
                text.replaceAll(",", "").toLowerCase()
            )
        );
        if (detectedPhrases.length > 0) {
            console.log(
                `Blocked phrases found: ${detectedPhrases.join(
                    ", "
                )} in ${text}`
            );
            return detectedPhrases.join(", ");
        }
        return false;
    };

    // Add repetition check function
    const checkForRepetition = (
        text: string,
        maxRepeats: number = 3
    ): false | string => {
        // Split text into words and check for consecutive repetitions
        const words = text.toLowerCase().split(/\s+/);
        let consecutiveCount = 1;
        let lastWord = "";

        for (const word of words) {
            // Skip empty words and very short words (like "a", "I", etc.)
            if (word.length <= 1) continue;

            if (word === lastWord) {
                consecutiveCount++;
                if (consecutiveCount > maxRepeats) {
                    const repetitionMessage = `Repeated word: "${word}" (${consecutiveCount} times)`;
                    console.log(`Repetition detected: ${repetitionMessage}`);
                    return repetitionMessage;
                }
            } else {
                consecutiveCount = 1;
                lastWord = word;
            }
        }
        return false;
    };

    let blockedPhrases = [
        "(",
        ")",
        "[",
        "]",
        "{",
        "}",
        "<",
        ">",
        "/",
        "@",
        "01",
        "+49",
        "added",
        "revised",
        "edit",
        "corona",
        "es tut mir leid aber",
        "es tut mir leid ich kann",
        "ich kann die anfrage nicht",
        " anfrage ",
        "ich entschuldige mich, aber",
        "ich entschuldige mich aber",
        " fügen ",
        " umformulieren ",
        " geringfügig ",
        " angefordert ",
        "<",
        ">",
        "Gesrächspartner",
        "undefined",
        "videochat",
        "user",
        "assistant",
        "abo ",
        " abo",
        "abzocke",
        "betrügen",
        "Betreiber",
        "Betrüger",
        "Penner",
        "Hurensohn",
        "Salihi",
        "Ergin",
        "Datenklau",
        "Bitch",
        "www.",
        "https://",
        "fake",
        "agb",
        "Youtube",
        "Reportage",
        "Interview",
        "STRG-F",
        "STRG",
        "NDR ",
        " NDR",
        ".com",
        ".de",
        ".net",
        "+4",
        "web.de",
        "(at)",
        "Angestellter",
        "Angestellte",
        "gmx.de",
        "Betrugsmasche",
        "Betreugen",
        "Betrueger",
        "Reingefallen",
        "Betrug",
        "verarscht",
        "verarschen",
        "strgf",
        "report",
        "Abgezogen",
        "googlemail.com",
        "gmail.com",
        "gmail",
        "googlemail",
        "Aleko",
        "ChatConnect",
        "Connect",
        "reingelegt",
        "chat ",
        " chat",
        "plötzlich",
        "butter bei die fische",
        "hurensohn",
        "bastard",
        "verpiss",
        " umformulierung",
        " umschreiben",
        " umformulieren",
        " umschreibung",
        "umformulierung ",
        "umschreiben ",
        "umformulieren ",
        "umschreibung ",
        "no city",
        "model ",
        " model",
        "hat deine nachricht",
        "hat dir eine freundschaftsanfrage gesendet",
        "hast dir eine freundschaftsanfrage gesendet",
        "hat dich zu ihren favoriten hinzugefügt",
        "hat dich hinzugefügt",
        "Meine Nummer ist",
        "hinzugefügt ein Foto",
        "meine adresse ist",
"knorke"
    ];

    if (siteInfos.origin == "kizzle") {
        blockedPhrases.push("bücherregal");
    }

    const isGermanTime = new Date().toLocaleString("en-US", {
        timeZone: "Europe/Berlin",
    });
    const germanHour = new Date(isGermanTime).getHours();

    if (!(germanHour >= 5 && germanHour < 10)) {
        blockedPhrases.push("guten morgen");
    } else if (!(germanHour >= 17 && germanHour < 22)) {
        blockedPhrases.push("guten abend");
    } else if (!(germanHour >= 20 || germanHour < 5)) {
        blockedPhrases.push("guten nacht");
    }

    // if (origin == "avz") {
    //     blockedPhrases.push("moderator");
    // } else if (
    //     origin == "gold" ||
    //     origin == "diamond" ||
    //     origin == "b3" ||
    //     origin == "cw"
    // ) {
    //     blockedPhrases.push("kein moderator");
    //     blockedPhrases.push("keine moderator");
    // }

    const blockedPhrasesMaleCustomerFemaleMod: string[] = [
        "Mein Schwanz",
        "Meinen Schwanz",
        "Deine Muschi",
        "Dein Mann",
        "Deiner Muschi",
        "Dir Muschi",
        "Deine Pussy",
        "Deiner Pussy",
        "Dir Pussy",
        "Deine Fotze",
        "Deine Votze",
        "Deiner Fotze",
        "Deiner Votze",
        "Meine Eier",
        "Meinen Eier",
        "Mein Sack",
        "Deine Vagina",
        "Deiner Vagina",
        "Deine Spalte",
        "Dein Spalt",
        "Dein Schlitz",
        "Deine feuchte",
        "Dein feuchtes",
        "Deine Löcher",
        "Deiner Löcher",
        "Dein Loch",
        "Mich reiten",
        "Dich lecken",
        "Meine Liebe",
        "Mein Sperma",
        "Meinen Sperma",
        "Meinem Sperma",
        "Mein Rohr",
        "Der einzige für dich",
        "Meine Eichel",
        "Meiner Eichel",
        "Meine Prostata",
        "Meiner Prostata",
        "Meine Sahne",
        "Deine Möse",
    ];

    const blockedPhrasesFemaleCustomerMaleMod: string[] = [
        "Meine Muschi",
        "Meiner Muschi",
        "Deine Frau",
        "Mir Muschi",
        "Meine Pussy",
        "Meiner Pussy",
        "Mir Pussy",
        "Meine Fotze",
        "Meine Votze",
        "Meiner Fotze",
        "Meiner Votze",
        "Dein Schwanz",
        "Deinen Schwanz",
        "Deine Eier",
        "Dein Sack",
        "Meine Vagina",
        "Meiner Vagina",
        "Meine Spalte",
        "Mein Spalt",
        "Mein Schlitz",
        "Meine feuchte",
        "Mein feuchtes",
        "Meine Löcher",
        "Meiner Löcher",
        "Mein Loch",
        "Dich reiten",
        "Mich lecken",
        "Mein Lieber",
        "Dein Sperma",
        "Deinen Sperma",
        "Deinem Sperma",
        "Dein Rohr",
        "Die Einzige für dich",
        "Deine Eichel",
        "Deiner Eichel",
        "Deine Prostata",
        "Deiner Prostata",
    ];

    const blockedPhrasesFemaleCustomerFemaleMod: string[] = [
        "Penis",
        "Schwanz",
        "Eier",
        "Dein Harter",
        "Mein Lieber",
        "Sperma",
        "Dein Mann",
        "Deine Prostata",
        "Deiner Prostata",
        "Meine Prostata",
        "Meiner Prostata",
    ];

    const blockedPhrasesMaleCustomerMaleMod: string[] = [
        "Muschi",
        "Vagina",
        "Fotze",
        "Votze",
        "Möse",
        "Deine feuchte",
        "Deine nasse",
        "Deine Frau",
    ];

    let allBlockedPhrases: string[] = [];

    // Check for word repetition first
    let repetitionBlocked = checkForRepetition(input);
    if (repetitionBlocked) {
        allBlockedPhrases.push(repetitionBlocked);
    }

    // Check for number + "Wörter" pattern
    const checkForNumberWorter = (text: string): false | string => {
        const numberWorterRegex = /\b\d+\s*wörter\b/i;
        const match = text.match(numberWorterRegex);
        if (match) {
            const detectedPhrase = match[0];
            console.log(
                `Blocked number + Wörter pattern found: ${detectedPhrase}`
            );
            return `Number + Wörter pattern: "${detectedPhrase}"`;
        }
        return false;
    };

    // Check for time patterns (hh:mm or hh:mm:ss)
    const checkForTimePattern = (text: string): false | string => {
        const timeRegex = /\b\d{1,2}:\d{2}(:\d{2})?\b/g;
        const matches = text.match(timeRegex);
        if (matches) {
            const detectedPatterns = matches.join(", ");
            console.log(`Blocked time pattern found: ${detectedPatterns}`);
            return `Time pattern: "${detectedPatterns}"`;
        }
        return false;
    };

    // Check for alphanumeric patterns (words with both letters and numbers)
    const checkForAlphanumericPattern = (text: string): false | string => {
        const alphanumericRegex = /\b(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]+\b/g;
        const matches = text.match(alphanumericRegex);
        if (matches) {
            const detectedPatterns = matches.join(", ");
            console.log(
                `Blocked alphanumeric pattern found: ${detectedPatterns}`
            );
            return `Alphanumeric pattern: "${detectedPatterns}"`;
        }
        return false;
    };

    // Check for moderator name + hinzugefügt pattern
    const checkForModeratorNamePattern = (text: string): false | string => {
        const moderatorName = siteInfos.metaData?.moderatorInfo?.name;
        if (!moderatorName || moderatorName.trim().length === 0) {
            return false;
        }

        // Escape special regex characters in the moderator name
        const escapedName = moderatorName.replace(
            /[.*+?^${}()|[\]\\]/g,
            "\\$&"
        );

        // Create regex that matches: moderator_name [up to 3 words] hinzugefügt
        const moderatorNameRegex = new RegExp(
            `\\b${escapedName}(?:\\s+\\w+){0,3}\\s*hinzugefügt\\b`,
            "i"
        );

        const match = text.match(moderatorNameRegex);
        if (match) {
            const detectedPhrase = match[0];
            console.log(
                `Blocked moderator name pattern found: ${detectedPhrase}`
            );
            return `Moderator name pattern: "${detectedPhrase}"`;
        }
        return false;
    };

    let numberWorterBlocked = checkForNumberWorter(input);
    if (numberWorterBlocked) {
        allBlockedPhrases.push(numberWorterBlocked);
    }

    // Check for time patterns
    let timePatternBlocked = checkForTimePattern(input);
    if (timePatternBlocked) {
        allBlockedPhrases.push(timePatternBlocked);
    }

    // Check for alphanumeric patterns
    let alphanumericBlocked = checkForAlphanumericPattern(input);
    if (alphanumericBlocked) {
        allBlockedPhrases.push(alphanumericBlocked);
    }

    // Check for moderator name + hinzugefügt pattern
    let moderatorNameBlocked = checkForModeratorNamePattern(input);
    if (moderatorNameBlocked) {
        allBlockedPhrases.push(moderatorNameBlocked);
    }

    let blocked = checkForbiddenWords(input, blockedPhrases);
    if (blocked) {
        allBlockedPhrases.push(blocked);
    }

    // Get gender information from siteInfos
    const customerGender = siteInfos.metaData.customerInfo.gender;
    const moderatorGender = siteInfos.metaData.moderatorInfo.gender;

    // Check based on gender combinations
    if (customerGender === "male" && moderatorGender === "female") {
        let blocked = checkForbiddenWords(
            input,
            blockedPhrasesMaleCustomerFemaleMod
        );
        if (blocked) {
            allBlockedPhrases.push(blocked);
        }
    }

    if (customerGender === "female" && moderatorGender === "male") {
        let blocked = checkForbiddenWords(
            input,
            blockedPhrasesFemaleCustomerMaleMod
        );
        if (blocked) {
            allBlockedPhrases.push(blocked);
        }
    }

    if (customerGender === "female" && moderatorGender === "female") {
        let blocked = checkForbiddenWords(
            input,
            blockedPhrasesFemaleCustomerFemaleMod
        );
        if (blocked) {
            allBlockedPhrases.push(blocked);
        }
    }

    if (customerGender === "male" && moderatorGender === "male") {
        let blocked = checkForbiddenWords(
            input,
            blockedPhrasesMaleCustomerMaleMod
        );
        if (blocked) {
            allBlockedPhrases.push(blocked);
        }
    }

    return allBlockedPhrases.length > 0 ? allBlockedPhrases.join(", ") : false;
}
