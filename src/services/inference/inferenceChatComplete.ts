// this file will handle all the inference calls

import OpenAI from "openai";
import { sendLogSnag } from "../logsnag";
import { PageType } from "../../models/SiteInfos";
import { Together } from "together-ai";
import { AIAnalysisService } from "./gemini";
// models should be able to switch between runpod llama models and gpt models
export async function handleInferenceChatCompletion(
    messages: any[],
    ai_model_config: any,
    origin: PageType
): Promise<{ content: string | null; thinkingContent: string | null } | null> {
    if (ai_model_config.model.includes("gpt")) {
        // ""
        return await openAIInference(messages, ai_model_config, origin);
    } else if (
        ai_model_config.model.toLowerCase().includes("meta") ||
        ai_model_config.model.toLowerCase().includes("qwen") ||
        ai_model_config.model.toLowerCase().includes("deepseek")
    ) {
        return await togetherAIInference(messages, ai_model_config, origin);
    } else if (ai_model_config.model.toLowerCase().includes("gemini")) {
        return await geminiInference(messages, ai_model_config, origin);
    }
    return { content: null, thinkingContent: null };
}

async function togetherAIInference(
    messages: any[],
    ai_model_config: any,
    origin: PageType
) {
    console.log("togetherAIInference", ai_model_config.model);
    try {
        const together = new Together({
            apiKey: process.env.TOGETHER_API_KEY,
        });

        const startTime = performance.now();
        const response = await together.chat.completions.create({
            messages: messages,
            ...ai_model_config,
            stream: false, // Set to false since we're handling a single response
        });
        const endTime = performance.now();
        const duration = endTime - startTime;

        console.log(`Together AI request took ${duration.toFixed(2)} ms`);

        let thinkingContent: string | null = null;

        let content = response.choices[0].message?.content;

        if (content?.includes("</think>")) {
            thinkingContent = content.split("</think>")[0] + "</think>";
            content = content.split("</think>")[1].replaceAll("\n", "");
        }

        if (!content) return null;

        return { content, thinkingContent };
    } catch (error) {
        console.error("Error during TogetherAI API call:", error);
        await sendLogSnag("togetherai_error", origin);
        return null;
    }
}

export async function openAIInference(
    messages: any[],
    ai_model_config: any,
    origin: PageType
) {
    try {
        const openai = new OpenAI({
            apiKey: process.env.OPENAI_API_KEY,
        });
        const response = await openai.chat.completions.create({
            messages: messages,
            ...ai_model_config,
        });

        let resText = response.choices[0].message.content;

        return { content: resText, thinkingContent: null };
    } catch (error) {
        // Handle error here, for example, log it or return a custom error message
        const errorObj = {
            error: error,
            messages: messages,
            ai_model_config: ai_model_config,
            origin: origin,
        };
        console.error(
            "Error during OpenAI API call:",
            JSON.stringify(errorObj)
        );
        await sendLogSnag("openai_error", origin);
        return null;
    }
}

export async function geminiInference(
    messages: any[],
    ai_model_config: any,
    origin: PageType
) {
    const systemMessage =
        messages.length > 0 && messages[0].role == "system"
            ? messages[0].content
            : "";
    const inferenceRes = await AIAnalysisService.generateTextFromPrompt(
        systemMessage,
        messages.filter((message) => message.role != "system"),
        ai_model_config.temperature,
        ai_model_config.topP,
        ai_model_config.model,
        ai_model_config.thinking
    );

    if (!inferenceRes.success) {
        throw new Error(`Failed gemini inference: ${inferenceRes.error}`);
    }

    return { content: inferenceRes.data!.generatedText, thinkingContent: null };
}
