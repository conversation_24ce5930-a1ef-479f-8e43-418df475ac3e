import { PageType } from "../../models/SiteInfos";
import { analyzeImage } from "../vison/visionServer";

export default async function profilePicAnalysis(
    origin: PageType,
    profilePicUrlUser?: string,
    profilePicUrlMod?: string
): Promise<{
    profilePicUrlModAnalysis: string | undefined;
    profilePicUrlModKeywords: string[] | undefined;
    profilePicUrlUserAnalysis: string | undefined;
    profilePicUrlUserKeywords: string[] | undefined;
}> {
    let profilePicUrlModAnalysis: string | undefined = undefined;
    let profilePicUrlModKeywords: string[] | undefined = undefined;
    let profilePicUrlUserAnalysis: string | undefined = undefined;
    let profilePicUrlUserKeywords: string[] | undefined = undefined;

    if (profilePicUrlMod) {
        if (profilePicUrlMod.includes("no-user-img")) {
            profilePicUrlModAnalysis = "Du hast kein Profilbild";
            profilePicUrlModKeywords = undefined;
        } else {
            let analyzedImage = await analyzeImage({
                imageUrls: [profilePicUrlMod],
                role: "assistant",
                origin,
                type: "profile_pic",
            });

            if (analyzedImage.data?.[0]?.analysis) {
                profilePicUrlModAnalysis =
                    analyzedImage.data[0].analysis.replace(
                        "Das Bild zeigt:",
                        "Das Profilbild von dir zeigt:"
                    );
            }

            if (analyzedImage.data?.[0]?.keywords) {
                profilePicUrlModKeywords = analyzedImage.data[0].keywords;
            }
        }
    }

    if (profilePicUrlUser) {
        if (profilePicUrlUser.includes("no-user-img")) {
            profilePicUrlUserAnalysis = "Der User hat kein Profilbild";
            profilePicUrlUserKeywords = undefined;
        } else {
            let analyzedImage = await analyzeImage({
                imageUrls: [profilePicUrlUser],
                role: "user",
                origin: origin,
                type: "profile_pic",
            });

            if (analyzedImage.data?.[0]?.analysis) {
                profilePicUrlUserAnalysis =
                    analyzedImage.data[0].analysis.replace(
                        "Das Bild zeigt:",
                        "Das Profilbild des Users zeigt:"
                    );
            }

            if (analyzedImage.data?.[0]?.keywords) {
                profilePicUrlUserKeywords = analyzedImage.data[0].keywords;
            }
        }
    }

    return {
        profilePicUrlModAnalysis,
        profilePicUrlModKeywords,
        profilePicUrlUserAnalysis,
        profilePicUrlUserKeywords,
    };
}
