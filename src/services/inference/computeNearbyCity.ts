import Prompt from "../../models/Pompt";
import { ComputedMetadata } from "../../models/SiteInfos";
import { findNearbyCity, Location } from "../googlePlaces/nearbyCity";

export async function computeNearbyCity(
    nearbyCity: { name: string; lat: number; long: number } | undefined,
    customerCityLocation: Location,
    prompt: Prompt,
    computedMetadata: ComputedMetadata
): Promise<{ name: string; lat: number; long: number } | undefined> {
    let nearbyCityComputed;

    if (
        !nearbyCity &&
        customerCityLocation &&
        prompt.prompt.includes("{{moderatorCity}}")
    ) {
        nearbyCityComputed = await findNearbyCity(customerCityLocation);
        return nearbyCityComputed ?? computedMetadata.nearbyCity;
    } else {
        if (nearbyCity) {
            nearbyCityComputed = nearbyCity;
            computedMetadata.nearbyCity = nearbyCity;
        }
        return computedMetadata.nearbyCity;
    }
}
