// Removed axios import as it's not needed for text-only generation
// import axios from 'axios';
// Removed Part import as it's image-specific
import {
    GoogleGenerativeAI,
    HarmBlockThreshold,
    HarmCategory,
    UsageMetadata,
} from "@google/generative-ai";
import dotenv from "dotenv";
import OpenAI from "openai";
// Removed unused prompt imports (already removed previously)

dotenv.config();

// Simplified result type for text generation
export type TextGenerationResult = {
    success: boolean;
    data?: {
        generatedText: string; // The generated text from the prompt
        model:
            | "gemini-2.0-flash-001"
            | "gemini-2.0-flash-lite-001"
            | "gemini-2.5-flash";
        processedAt: Date;
        usage?: UsageMetadata;
    };
    error?: string;
};

export class AIAnalysisService {
    private static genAI = new GoogleGenerativeAI(
        process.env.GEMINI_API_KEY || ""
    );
    /**
     * Helper function to call Gemini API with retry logic for transient errors.
     * Now expects a GenerateContentRequest object.
     */

    /**
     * Generates text using Gemini AI based on a given prompt and optional generation parameters.
     */
    // Add optional topP parameter
    private static async generateText(
        prompt: string,
        messages: OpenAI.ChatCompletionMessageParam[],
        model:
            | "gemini-2.0-flash-001"
            | "gemini-2.0-flash-lite-001"
            | "gemini-2.5-flash",
        temperature?: number,
        topP?: number,
        thinking?: boolean
    ): Promise<{ responseText: string; usage?: UsageMetadata }> {
        try {
            console.log(
                `--- Generating Text with Prompt (Temp: ${
                    temperature ?? "default"
                }, TopP: ${topP ?? "default"}) ---` // Log topP
            );

            const _model = this.genAI.getGenerativeModel({
                model: model,
                systemInstruction: prompt,
                safetySettings: [
                    {
                        category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                        threshold: HarmBlockThreshold.BLOCK_NONE,
                    },
                    {
                        category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                        threshold: HarmBlockThreshold.BLOCK_NONE,
                    },
                    {
                        category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                        threshold: HarmBlockThreshold.BLOCK_NONE,
                    },
                    {
                        category: HarmCategory.HARM_CATEGORY_HARASSMENT,
                        threshold: HarmBlockThreshold.BLOCK_NONE,
                    },
                ],
            });

            // Format messages for Gemini API
            // Gemini doesn't support system role, so we need to format differently
            const formattedMessages = messages
                .filter((m) => m.role !== "system") // Filter out system messages first
                .map((m) => ({
                    role: m.role === "user" ? "User:" : "Model:",
                    parts: [{ text: m.content as string }],
                }));

            const formattedUserMessage = formattedMessages
                .map((m) => {
                    return m.role + ": " + m.parts[0].text;
                })
                .join("\n");

            let chatConfig: any = {
                temperature: temperature ?? 1,
                topP: topP ?? 0.95,
                topK: 40,
                maxOutputTokens: 8192,
                responseMimeType: "text/plain",
            };

            if (model === "gemini-2.5-flash") {
                if (thinking) {
                    chatConfig.thinkingConfig = {
                        thinkingBudget: -1,
                    };
                } else {
                    chatConfig.thinkingConfig = {
                        thinkingBudget: 0,
                    };
                }
            }

            // Create a chat session with the model
            const chatSession = _model.startChat({
                generationConfig: chatConfig,
            });

            // Send the prompt as a message to the chat session
            const result = await chatSession.sendMessage(formattedUserMessage);

            // Safely access the text
            const responseText = result.response.text();

            const usage = result.response.usageMetadata;

            return { responseText, usage };
        } catch (error) {
            // Re-throw a more specific error to be caught by the public method
            throw new Error(
                `Failed to generate text with Gemini. Reason: ${
                    error instanceof Error ? error.message : String(error)
                }`
            );
        }
    }

    /**
     * Main public method to generate text from a single prompt, with optional temperature and topP.
     */
    public static async generateTextFromPrompt(
        prompt: string,
        messages: OpenAI.ChatCompletionMessageParam[],
        // Add optional temperature parameter here
        temperature?: number,
        // Add optional topP parameter here
        topP?: number,
        model:
            | "gemini-2.0-flash-001"
            | "gemini-2.5-flash"
            | "gemini-2.0-flash-lite-001" = "gemini-2.5-flash",
        thinking?: boolean
    ): Promise<TextGenerationResult> {
        // Return the text-specific result type
        try {
            // Pass the temperature and topP down to the private method
            let generatedText = await this.generateText(
                prompt,
                messages,
                model,
                temperature,
                topP,
                thinking
            );

            // Return a successful result
            return {
                success: true,
                data: {
                    generatedText: generatedText.responseText,
                    // Ensure this matches the actual model used
                    model: model,
                    processedAt: new Date(),
                    usage: generatedText.usage,
                },
            };
        } catch (error) {
            // Catch errors from generateText or other unexpected issues
            console.error(`Failed to process text generation request:`, error);
            return {
                success: false,
                // Provide the error message in the failure case
                error:
                    error instanceof Error
                        ? error.message
                        : "An unknown error occurred during text generation.",
            };
        }
    }
}

export function calculateCosts(
    model:
        | "gemini-2.0-flash-001"
        | "gemini-2.5-flash"
        | "gemini-2.0-flash-lite-001",
    usage?: UsageMetadata
): number {
    if (!usage) {
        return 0;
    }

    const inputTokens = usage.promptTokenCount || 0;
    const outputTokens = usage.candidatesTokenCount || 0;

    // Pricing per 1M tokens in USD (paid tier)
    let inputPricePerMillion: number;
    let outputPricePerMillion: number;

    switch (model) {
        case "gemini-2.5-flash":
            // Gemini 2.5 Flash pricing
            inputPricePerMillion = 0.3; // $0.30 per 1M tokens for text/image/video
            outputPricePerMillion = 2.5; // $2.50 per 1M tokens (including thinking tokens)
            break;

        case "gemini-2.0-flash-lite-001":
            // Gemini 2.5 Flash-Lite Preview pricing (assuming 2.0 flash-lite uses same pricing)
            inputPricePerMillion = 0.075; // $0.10 per 1M tokens for text/image/video
            outputPricePerMillion = 0.3; // $0.40 per 1M tokens (including thinking tokens)
            break;

        case "gemini-2.0-flash-001":
            // Assuming same pricing as Gemini 2.5 Flash for now
            // (adjust if different pricing is found)
            inputPricePerMillion = 0.1;
            outputPricePerMillion = 0.4;
            break;

        default:
            console.warn(`Unknown model: ${model}, returning 0 cost`);
            return 0;
    }

    // Calculate costs
    const inputCost = (inputTokens / 1_000_000) * inputPricePerMillion;
    const outputCost = (outputTokens / 1_000_000) * outputPricePerMillion;
    const totalCost = inputCost + outputCost;

    return parseFloat(totalCost.toFixed(6)); // Round to 6 decimal places for precision
}
