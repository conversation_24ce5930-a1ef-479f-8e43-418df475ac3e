// Format cloudworkers messages to openai messages

import OpenAI from "openai";
import { Message, PageType } from "../../models/SiteInfos";
import { analyzeImage } from "../vison/visionServer";

// extension adds the prefix IMAGEUPLOAD: to a message that has an image in it
export async function messagesToOpenAIMessages(
    origin: PageType,
    messages: Message[]
): Promise<OpenAI.Chat.Completions.ChatCompletionMessageParam[]> {
    const roleMap = {
        sent: "assistant",
        received: "user",
        system: "system",
    };

    const filteredMessages = messages
        .map((m) => ({
            ...m,
            text: m.text ? m.text : "",
        }))
        .filter(
            (message) =>
                (message.text != "" && message.text) || message.imageSrc
        );

    const mappedMessges = await Promise.all(
        filteredMessages.map(async (message, index) => {
            let newMessageText = message.text.replace("IMAGEUPLOAD: ", "");

            if (message.imageSrc) {
                console.log("analyzing image...");
                let analyzedImage = await analyzeImage({
                    imageUrls: [message.imageSrc],
                    role: roleMap[message.type] as "user" | "assistant",
                    origin: origin,
                    type: "inline_chat",
                });

                if (analyzedImage.data?.[0]?.analysis) {
                    newMessageText =
                        "<Nachricht>" +
                        message.text +
                        "</Nachricht>" +
                        "\n\n" +
                        analyzedImage.data[0].analysis.replace(
                            "Das Bild zeigt:",
                            `Hier schicke ich dir ein Bild <Bildbeschreibung>`
                        ) +
                        "</Bildbeschreibung>";
                }
            }

            if (message.text.includes("Automated text:")) {
                return {
                    //@ts-ignore
                    role: "assistant",
                    content: message.text.replace("Automated text: ", ""),
                    imageSrc: message.imageSrc,
                };
            }

            if (/\w+@\*+/.test(message.text) && message.type == "received") {
                return {
                    //@ts-ignore
                    role: "user",
                    content: message.text.replace(
                        /\w+@\*+/g,
                        "(E-Mail-Adresse verborgen)"
                    ),
                    imageSrc: message.imageSrc,
                };
            }

            // This code checks if a message contains asterisks (*) and is from the user (received)
            // If so, it replaces all sequences of asterisks with "(Telefonnummer verborgen)"
            // which means "phone number hidden" in German
            // This is likely a privacy feature to mask phone numbers that were redacted with asterisks
            // Note: The console log message about "imageupload" appears to be incorrect
            if (/\*+/.test(message.text) && message.type == "received") {
                return {
                    //@ts-ignore
                    role: roleMap[message.type],
                    content: message.text.replace(
                        /\*+/g,
                        "(Telefonnummer verborgen)"
                    ),
                    imageSrc: message.imageSrc,
                };
            }

            return {
                //@ts-ignore
                role: roleMap[message.type],
                content: newMessageText,
                imageSrc: message.imageSrc,
            };
        })
    );

    return mappedMessges as OpenAI.Chat.Completions.ChatCompletionMessageParam[];
}
