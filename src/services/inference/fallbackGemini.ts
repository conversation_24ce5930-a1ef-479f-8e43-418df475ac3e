// 1. check if messages are nsfw or not - prompt id 94 - gemini
// 2. if nsfw, use prompt id 95. it is qwen3 now
// 3. if not nsfw, return undefined - will fallback to old gpt models

import OpenAI from "openai";
import { ExtractedMetadata, PageType, SiteInfos } from "../../models/SiteInfos";
import { mergePrompt } from "./mergePrompt";
import { getPromptDB } from "../supabase/promptConfig";
import { openaiMessagesToSystemMessageString } from "./inferenceGemini";
import { AIAnalysisService } from "./gemini";
import { createAnalyticsItem } from "../supabase/chatCompleteAnalyticsService";
import { handleInferenceChatCompletion } from "./inferenceChatComplete";
import Prompt from "../../models/Pompt";

export async function fallbackGemini(
    messages: OpenAI.ChatCompletionMessageParam[],
    origin: PageType,
    metadata: ExtractedMetadata,
    siteInfos: SiteInfos
) {
    const prompt = await getPromptDB(94);
    if (!prompt) {
        throw new Error("Prompt not to check nsfw not found");
    }

    prompt.prompt = prompt.prompt.replace(
        "{{previousMessages}}",
        openaiMessagesToSystemMessageString(messages)
    );
    let isNsfw = false;

    // TODO: - do this with grok
    if (
        origin != "justlo" ||
        (origin == "justlo" &&
            siteInfos.metaData.ins &&
            siteInfos.metaData.ins > 200)
    ) {
        const inferenceRes = await AIAnalysisService.generateTextFromPrompt(
            prompt.prompt,
            [
                {
                    role: "user",
                    content:
                        "überprüfe ob sich die Konversation über sexuelle Inhalte handelt oder nicht. Antworte entweder mit 'sexuell' oder 'nicht sexuell'",
                },
            ],
            prompt.ai_model_config.temperature,
            prompt.ai_model_config.topP,
            prompt.ai_model_config.model
        );

        if (!inferenceRes.success) {
            throw new Error("Failed to check nsfw");
        }

        const nsfw = inferenceRes.data!.generatedText.replaceAll("\n", "");

        const validResponses = ["sexuell", "nicht sexuell"];

        if (!validResponses.includes(nsfw)) {
            return await fallbackGemini(messages, origin, metadata, siteInfos);
        }

        await createAnalyticsItem({
            chat_complete_prompt_id: prompt.id!,
            origin_website: origin,
            metadata: metadata,
            ai_generated_message: nsfw,
            prev_messages: messages,
            language: "DE",
            ai_model: prompt.ai_model_config.model,
        });

        isNsfw = nsfw == "sexuell";
    }

    if (!isNsfw) {
        // if not nsfw, return undefined - will fallback to old gpt models
        return undefined;
    }

    let nsfwPrompt: Prompt | null;

    if (
        metadata.customerGender == "Frau" &&
        metadata.moderatorGender == "Mann"
    ) {
        // for hetero women
        nsfwPrompt = await getPromptDB(107);
    } else {
        // for hetero men, gays and lesbians
        nsfwPrompt = await getPromptDB(95);
    }

    if (!nsfwPrompt) {
        throw new Error("Prompt not to check nsfw not found");
    }

    const nsfwMessages = [
        {
            role: "system",
            content: nsfwPrompt.prompt,
        },
        // rest of the array are the messages
        ...messages,
    ];

    const nsfwRes = await AIAnalysisService.generateTextFromPrompt(
        nsfwPrompt.prompt,
        messages,
        nsfwPrompt.ai_model_config.temperature,
        nsfwPrompt.ai_model_config.topP,
        "gemini-2.5-flash",
        nsfwPrompt.ai_model_config.thinking
    );

    if (
        nsfwRes.success &&
        nsfwRes.data &&
        nsfwRes.data.generatedText &&
        nsfwRes.data.generatedText !== ""
    ) {
        await createAnalyticsItem({
            chat_complete_prompt_id: nsfwPrompt.id!,
            origin_website: origin,
            metadata: metadata,
            ai_generated_message: nsfwRes.data.generatedText,
            prev_messages: [
                {
                    role: "system",
                    content: nsfwPrompt.prompt,
                },
                ...messages,
            ],
            language: "DE",
            ai_model: nsfwPrompt.ai_model_config.model,
        });

        return nsfwRes;
    }

    return undefined;
}
