import { OpenAI } from "openai";
import { mergePrompt } from "./mergePrompt";
import Prompt from "../../models/Pompt";

interface Turn {
    role: string;
    parts: { text: string }[];
}

interface DatasetItem {
    systemInstruction: {
        role: string;
        parts: { text: string }[];
    };
    contents: Turn[];
}

export function processDataToDataset(
    data: {
        prev_messages: OpenAI.ChatCompletionMessageParam[];
        corrected_output?: string;
        ai_generated_message: string;
        metadata: {
            extractedMetadata: any;
        };
    }[],
    prompt: Prompt
): DatasetItem[] {
    const dataset = data.map((item) => {
        // Combine previous messages and the final AI message into one list
        const allMessages = [
            ...item.prev_messages.slice(1).map((message) => ({
                role: message.role === "assistant" ? "model" : "user", // Standardize roles
                text: message.content as string,
            })),
            {
                // Add the final model message
                role: "model",
                text:
                    item.corrected_output && item.corrected_output.trim() !== ""
                        ? item.corrected_output
                        : item.ai_generated_message,
            },
        ];

        // Group consecutive messages by role using reduce
        const groupedContents = allMessages.reduce((acc, currentMessage) => {
            // Get the last turn added to the accumulator
            const lastTurn = acc.length > 0 ? acc[acc.length - 1] : null;

            if (lastTurn && lastTurn.role === currentMessage.role) {
                // If the role is the same as the last turn, add the text to its 'parts'
                lastTurn.parts.push({ text: currentMessage.text });
            } else {
                // If the role is different or it's the first message, create a new turn object
                acc.push({
                    role: currentMessage.role,
                    parts: [{ text: currentMessage.text }], // Start parts array with current message text
                });
            }
            return acc; // Return the updated accumulator
        }, [] as Turn[]); // Initialize with an empty array typed correctly

        // Return the structured dataset item
        return {
            systemInstruction: {
                role: "system",
                parts: [
                    {
                        text: mergePrompt(
                            prompt,
                            item.metadata.extractedMetadata
                        ),
                    },
                ],
            },
            contents: groupedContents, // Use the grouped and structured contents
        };
    });
    return dataset;
}
