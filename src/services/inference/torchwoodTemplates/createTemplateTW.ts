import { Message, PageType, SiteInfos } from "../../../models/SiteInfos";
import { cleanupOutput } from "../../../util/cleanup";
import { appendEmoji } from "../../../util/emojiAppender";
import checkForBlockedPhrases from "../../forbiddenWords";
import lengthChecker from "../../outputChecker/lengthChecker/lengthChecker";
import ligamentolysis from "../../outputChecker/lengthChecker/ligamentolysis";
import {
    asa1FromTemplates,
    icebreakerResponseFromTemplates,
    reactivationFromTemplates,
} from "./reactivationFromTemplates";

interface TemplateConfig {
    templateFunction: (
        siteInfos: SiteInfos
    ) => Promise<string | undefined | null>;
    promptType: string;
    shouldAppendEmoji: boolean;
    delay: number;
}

export default async function createTemplateTW(
    messages: Message[],
    siteInfos: SiteInfos,
    origin: PageType,
    minLength: number
) {
    console.log("Attempting to send reactivation template for torchwood");

    const templateConfig = getTemplateConfig(messages);
    if (!templateConfig) return;

    const template = await processTemplate(
        templateConfig,
        siteInfos,
        minLength,
        origin
    );

    const blockedPhrases = checkForBlockedPhrases(template!, siteInfos);

    // runs for every user
    if (blockedPhrases) {
        return;
    }

    if (template) {
        await new Promise((resolve) =>
            setTimeout(resolve, templateConfig.delay)
        );
        return createResponse(template, templateConfig.promptType, siteInfos);
    }
}

function getTemplateConfig(messages: Message[]): TemplateConfig | null {
    const lastTwo = messages.slice(-2);
    const lastOne = messages.slice(-1);

    // Two consecutive "sent" messages -> reactivation
    if (
        messages.length >= 2 &&
        lastTwo[0].type === "sent" &&
        lastTwo[1].type === "sent"
    ) {
        return {
            templateFunction: reactivationFromTemplates,
            promptType: "reactivation",
            shouldAppendEmoji: true,
            delay: 15000,
        };
    }

    // // "received" then "sent" -> asa1
    // if (
    //     messages.length >= 2 &&
    //     lastTwo[0].type === "received" &&
    //     lastTwo[1].type === "sent"
    // ) {
    //     return {
    //         templateFunction: asa1FromTemplates,
    //         promptType: "asa1",
    //         shouldAppendEmoji: true,
    //         delay: 5000,
    //     };
    // }

    // Icebreaker response
    if (
        messages.length >= 1 &&
        lastOne[0].type === "received" &&
        lastOne[0].text.includes("Lass uns das Eis brechen")
    ) {
        return {
            templateFunction: icebreakerResponseFromTemplates,
            promptType: "icebreaker",
            shouldAppendEmoji: true,
            delay: 5000,
        };
    }

    return null;
}

async function processTemplate(
    config: TemplateConfig,
    siteInfos: SiteInfos,
    minLength: number,
    origin: PageType
): Promise<string | null> {
    for (let attempt = 0; attempt < 10; attempt++) {
        if (attempt > 0) {
            console.log(`Trying attempt ${attempt} for ${config.promptType}`);
        }

        let template = await config.templateFunction(siteInfos);
        console.log(`${config.promptType}Template`, template);

        if (!template) continue;

        // Clean up the output
        template = cleanupOutput(template, siteInfos);
        if (!template) continue;

        // Check and extend length if needed
        const deltaTextLength = lengthChecker(template, minLength, origin);
        if (deltaTextLength < 0) {
            const extendedTemplate = await ligamentolysis(
                template,
                minLength,
                origin
            );
            if (extendedTemplate) {
                template = extendedTemplate;
            } else {
                continue; // Try another template
            }
        }

        // Final cleanup and emoji processing
        template = cleanupOutput(template, siteInfos);
        if (!template) continue;
        console.log(`${config.promptType}Template after cleanup`, template);

        if (config.shouldAppendEmoji) {
            template = template.replace(
                /[\u{1F300}-\u{1F9FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F000}-\u{1F02F}]|[\u{1F0A0}-\u{1F0FF}]|[\u{1F100}-\u{1F64F}]|[\u{1F680}-\u{1F6FF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{1F200}-\u{1F2FF}]|[\u{1F600}-\u{1F64F}]|[\u{1F680}-\u{1F6FF}]|[\u{1F700}-\u{1F77F}]|[\u{1F780}-\u{1F7FF}]|[\u{1F800}-\u{1F8FF}]|[\u{1F900}-\u{1F9FF}]|[\u{1FA00}-\u{1FA6F}]|[\u{1FA70}-\u{1FAFF}]|[\u{1FAB0}-\u{1FABF}]|[\u{1FAC0}-\u{1FAFF}]|[\u{1FAD0}-\u{1FAFF}]|[\u{1FAE0}-\u{1FAFF}]|[\u{1FAF0}-\u{1FAFF}]|[\u{1FB00}-\u{1FBFF}]|[\u{1FC00}-\u{1FCFF}]|[\u{1FD00}-\u{1FDFF}]|[\u{1FE00}-\u{1FEFF}]|[\u{1FF00}-\u{1FFFF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{2B00}-\u{2BFF}]|[\u{2C00}-\u{2CFF}]|[\u{2D00}-\u{2DFF}]|[\u{2E00}-\u{2EFF}]|[\u{2F00}-\u{2FFF}]|[\u{3000}-\u{303F}]|[\u{3040}-\u{309F}]|[\u{30A0}-\u{30FF}]|[\u{3100}-\u{312F}]|[\u{3130}-\u{318F}]|[\u{3190}-\u{319F}]|[\u{31A0}-\u{31BF}]|[\u{31C0}-\u{31EF}]|[\u{31F0}-\u{31FF}]|[\u{3200}-\u{32FF}]|[\u{3300}-\u{33FF}]|[\u{3400}-\u{4DBF}]|[\u{4DC0}-\u{4DFF}]|[\u{4E00}-\u{9FFF}]|[\u{A000}-\u{A48F}]|[\u{A490}-\u{A4CF}]|[\u{A4D0}-\u{A4FF}]|[\u{A500}-\u{A63F}]|[\u{A640}-\u{A69F}]|[\u{A6A0}-\u{A6FF}]|[\u{A700}-\u{A71F}]|[\u{A720}-\u{A7FF}]|[\u{A800}-\u{A82F}]|[\u{A830}-\u{A83F}]|[\u{A840}-\u{A87F}]|[\u{A880}-\u{A8DF}]|[\u{A8E0}-\u{A8FF}]|[\u{A900}-\u{A92F}]|[\u{A930}-\u{A95F}]|[\u{A960}-\u{A97F}]|[\u{A980}-\u{A9DF}]|[\u{A9E0}-\u{A9FF}]|[\u{AA00}-\u{AA5F}]|[\u{AA60}-\u{AA7F}]|[\u{AA80}-\u{AADF}]|[\u{AAE0}-\u{AAFF}]|[\u{AB00}-\u{AB2F}]|[\u{AB30}-\u{AB6F}]|[\u{AB70}-\u{ABBF}]|[\u{ABC0}-\u{ABFF}]|[\u{AC00}-\u{D7AF}]|[\u{D7B0}-\u{D7FF}]|[\u{D800}-\u{DB7F}]|[\u{DB80}-\u{DBFF}]|[\u{DC00}-\u{DFFF}]|[\u{E000}-\u{F8FF}]|[\u{F900}-\u{FAFF}]|[\u{FB00}-\u{FB4F}]|[\u{FB50}-\u{FDFF}]|[\u{FE00}-\u{FE0F}]|[\u{FE10}-\u{FE1F}]|[\u{FE20}-\u{FE2F}]|[\u{FE30}-\u{FE4F}]|[\u{FE50}-\u{FE6F}]|[\u{FE70}-\u{FEFF}]|[\u{FF00}-\u{FFEF}]|[\u{FFF0}-\u{FFFF}]/gu,
                ""
            );
            template = await appendEmoji(template, siteInfos);
            console.log(
                `${config.promptType}Template after appendEmoji`,
                template
            );
        }

        // Add blocked phrases check
        const blockedPhrases = checkForBlockedPhrases(template, siteInfos);

        // runs for every user
        if (blockedPhrases != false) {
            continue;
        }

        return template;
    }

    return null;
}

function createResponse(
    resText: string,
    promptType: string,
    siteInfos: SiteInfos
) {
    return {
        resText,
        promptType,
        disableAutoSend: false,
        summary: {
            assistant: {},
            user: {},
        },
        analyticsItemId: 1,
        extractedMetadata: {},
        chatId: siteInfos.metaData.chatId,
        assetsToSend: undefined,
        alert: "",
    };
}
