import { SiteInfos } from "../../../models/SiteInfos";
import supabaseAdminClient from "../../../util/createClient";
import path from "path";
import fs from "fs";
import rewriteResponse from "../../rewrite/rewriteResponse";
import { ModeratorRewriteInfos } from "../../rewrite/rewriteAttributes";
import { getRewrittenAttributes } from "../../rewrite/rewriteAttributes";

/**
 * Fetch reactivation templates from the database
 * based on username from extension.
 * if none available, retrurn undefined
 */

/**
 * Helper function to apply rewrite logic to a selected message
 */
async function applyRewriteLogic(
    selectedMessage: string,
    siteInfos: SiteInfos
): Promise<string> {
    // Apply rewrite if rewriteConfig exists and origin is avz or torchwood
    if (siteInfos.metaData.rewriteConfig) {
        try {
            const rewrittenMessage = await rewriteResponse(
                selectedMessage,
                siteInfos,
                siteInfos.origin
            );
            return rewrittenMessage;
        } catch (error) {
            console.error("Error during rewrite:", error);
            return selectedMessage; // Return original message if rewrite fails
        }
    } else {
        // rewrite with random adjectives
        const moderatorRewriteInfos: ModeratorRewriteInfos = {
            age: siteInfos.metaData.moderatorInfo.birthDate.age ?? 20,
            gender: siteInfos.metaData.moderatorInfo.gender,
        };

        const rewriteAttributes = await getRewrittenAttributes(
            "df",
            moderatorRewriteInfos
        );

        // if (origin == "myloves") {
        //     rewriteAttributes.push("Kürzere Antwort");
        // }

        siteInfos.metaData.rewriteConfig = {
            attributes: rewriteAttributes,
            rewriteAge: moderatorRewriteInfos.age.toString(),
            rewriteGender: moderatorRewriteInfos.gender,
            moderatorUsername: siteInfos.metaData.moderatorInfo.username,
        };

        console.log("rewriteAttributes", rewriteAttributes);

        const rewriteRes = await rewriteResponse(
            selectedMessage,
            siteInfos,
            "df"
        );
        return rewriteRes;
    }
}

/**
 * Generic function to get a random message from a CSV file and apply rewrite logic
 */
async function getRandomMessageFromCSV(
    csvFileName: string,
    siteInfos: SiteInfos
): Promise<string | undefined> {
    try {
        // Read the CSV file
        const csvFilePath = path.join(
            __dirname,
            `../../../util/${csvFileName}`
        );
        const fileContent = await fs.promises.readFile(csvFilePath, "utf-8");

        if (!fileContent) {
            console.error("No file content found in CSV file");
            return undefined;
        }

        // Parse the CSV file
        const messages = fileContent
            .split("\n")
            .map((line) => line.trim())
            .filter(
                (line) => line && !line.startsWith("thumb_upthumb_down") // Only filter out the header
            );

        if (messages.length === 0) {
            console.error("No messages found in CSV file");
            return undefined;
        }

        // Select a random message
        const randomIndex = Math.floor(Math.random() * messages.length);
        const selectedMessage = messages[randomIndex];

        console.log("Selected random message:", selectedMessage);

        // Apply rewrite logic
        return await applyRewriteLogic(selectedMessage, siteInfos);
    } catch (error) {
        console.error("Error reading or parsing CSV file:", error);
        return undefined;
    }
}

export async function reactivationFromTemplates(
    siteInfos: SiteInfos
): Promise<string | undefined> {
    console.log("Starting reactivationFromTemplates function");
    return await getRandomMessageFromCSV("tw-asa.csv", siteInfos);
}

export async function asa1FromTemplates(
    siteInfos: SiteInfos
): Promise<string | undefined> {
    console.log("Starting asa1FromTemplates function");
    return await getRandomMessageFromCSV("tw-asa-1.csv", siteInfos);
}

export async function icebreakerResponseFromTemplates(
    siteInfos: SiteInfos
): Promise<string | undefined> {
    console.log("Starting icebreakerResponseFromTemplates function");
    return await getRandomMessageFromCSV("tw-icebreaker.csv", siteInfos);
}
