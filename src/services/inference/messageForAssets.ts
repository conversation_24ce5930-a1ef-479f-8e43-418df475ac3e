// this file is used to generate a message for the assets

import OpenAI from "openai";
import {
    ComputedMetadata,
    ExtractedMetadata,
    ImageAnalysisData,
    PageType,
    SiteInfos,
} from "../../models/SiteInfos";
import { cleanupOutput } from "../../util/cleanup";
import { appendEmoji } from "../../util/emojiAppender";
import { inferenceGemini } from "./inferenceGemini";
import { PromptType } from "../promptType/promptTypeChatCompletion";
import { generateChatCompletion } from "./generateChatCompletion";
import checkForBlockedPhrases from "../forbiddenWords";

export default async function messageForAssets(
    assetsToSend: ImageAnalysisData[] | undefined,
    siteInfos: SiteInfos,
    extractedMetadata: ExtractedMetadata,
    computedMetadata: ComputedMetadata,
    messages: OpenAI.ChatCompletionMessageParam[] & { imageSrc?: string }[],
    origin: PageType
) {
    if (!assetsToSend || assetsToSend.length == 0) {
        return undefined;
    }

    console.log("messages on messageForAssets", messages);

    // Process all assets in parallel
    const processedAssetsToSend = (
        await Promise.all(
            assetsToSend.map(async (asset) => {
                let newExtractedMetadata: ExtractedMetadata = {
                    ...extractedMetadata,
                    photoDescription: asset.analysis,
                    openaiMessages: messages
                        .filter((m) => m.role != "system")
                        .slice(-4)
                        .map(
                            (m) =>
                                `${m.role == "user" ? "User" : "Model"}: ${
                                    m.content
                                }`
                        )
                        .join("\n"),
                };

                const filteredMessages = messages
                    .filter((m) => m.role != "system")
                    .slice(-4);

                console.log(
                    "inference gemini text for asset",
                    filteredMessages
                );

                const inferenceRes = await inferenceGemini(
                    newExtractedMetadata,
                    computedMetadata,
                    siteInfos,
                    origin,
                    filteredMessages,
                    PromptType.MESSAGE_FOR_ASSET,
                    asset
                );

                console.log("inferenceRes", inferenceRes);

                if (inferenceRes) {
                    let resText = cleanupOutput(inferenceRes, siteInfos)!;

                    // Add blocked phrases check
                    const blockedPhrases = checkForBlockedPhrases(
                        resText!,
                        siteInfos
                    );

                    // runs for every user
                    if (blockedPhrases != false) {
                        return undefined;
                    }

                    if (
                        origin == "gold" ||
                        origin == "wifu" ||
                        origin == "xloves" ||
                        origin == "lacarna" ||
                        origin == "myloves" ||
                        origin == "onlydates69" ||
                        origin == "whatsmeet"
                    ) {
                        resText = await appendEmoji(resText!, siteInfos);
                    }

                    return {
                        ...asset,
                        resText,
                    };
                }
                console.log("no inferenceRes");
                // If inferenceRes is falsy, skip this asset
                return undefined;
            })
        )
    ).filter(Boolean) as ImageAnalysisData[]; // Remove any undefined results

    return processedAssetsToSend;
}
