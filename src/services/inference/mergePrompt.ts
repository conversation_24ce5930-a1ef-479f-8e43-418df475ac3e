import Prompt from "../../models/Pompt";
import { ExtractedMetadata } from "../../models/SiteInfos";

export function mergePromptWithObject(prompt: string, attributes: any): string {
    let copyPrompt = prompt;

    // Handle all attributes keys dynamically
    Object.entries(attributes).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
            copyPrompt = copyPrompt.replaceAll(
                `{{${key}}}`,
                typeof value === "object"
                    ? JSON.stringify(value).replaceAll("\n", " ")
                    : String(value)
            );
        } else {
            copyPrompt = copyPrompt.replaceAll(`{{${key}}}`, "");
        }
    });

    // Clean up any remaining template variables that weren't in attributes
    copyPrompt = copyPrompt.replace(/\{\{[^}]+\}\}/g, "");

    return copyPrompt;
}

export function mergePrompt(
    prompt: Prompt,
    extractedMetadata: ExtractedMetadata
): string {
    let copyPrompt = prompt.prompt;
    //     process.env.ENVIRONMENT === "development" && prompt.dev_prompt?.trim()
    //         ? prompt.dev_prompt
    //         : prompt.prompt;

    // if (process.env.ENVIRONMENT === "development") {
    //     console.log(
    //         prompt.dev_prompt?.trim()
    //             ? "Dev mode and dev prompt found"
    //             : "Dev mode and no dev prompt found"
    //     );
    // }

    // the fpc reactivation prompts
    if (prompt.id == 53 || prompt.id == 52) {
        if (extractedMetadata.minLength == 220) {
            copyPrompt = copyPrompt.replaceAll("35 Wörter", "55 Wörter");
        }
    }

    // Use the generic function for the rest of the merging
    return mergePromptWithObject(copyPrompt, extractedMetadata);
}
