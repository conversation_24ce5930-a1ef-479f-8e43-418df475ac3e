import {
    GenerateContentConfig,
    GenerateContentParameters,
    GenerateContentResponse,
    GoogleGenAI,
    HarmBlockThreshold,
    HarmCategory,
} from "@google/genai";
import dotenv from "dotenv";
import { getPromptDB } from "../supabase/promptConfig";
import {
    ComputedMetadata,
    ExtractedMetadata,
    ImageAnalysisData,
    PageType,
    SiteInfos,
} from "../../models/SiteInfos";
import { mergePrompt } from "./mergePrompt";
import { OpenAI } from "openai";
import {
    createAnalyticsItem,
    generateAnalyticsItem,
} from "../supabase/chatCompleteAnalyticsService";
dotenv.config();

import { GoogleAuth } from "google-auth-library";
import { PromptType } from "../promptType/promptTypeChatCompletion";
import { AIAnalysisService, TextGenerationResult } from "./gemini";

const auth = new GoogleAuth({
    credentials: {
        type: "service_account",
        project_id: process.env.GOOGLE_PROJECT_ID,
        private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, "\n"),
        client_email: process.env.GOOGLE_CLIENT_EMAIL,
    },
    scopes: ["https://www.googleapis.com/auth/cloud-platform"],
});

export async function promptForGemini(
    siteInfos: SiteInfos,
    extractedMetadata: ExtractedMetadata,
    origin: PageType,
    openaiMessages: OpenAI.ChatCompletionMessageParam[],
    originalPromptType: PromptType
) {
    let prompt = undefined;
    let mergedPrompt = undefined;

    console.log("openaiMessages", openaiMessages);

    if (openaiMessages.length == 0) {
        prompt = await getPromptDB(102); // message for asset
        mergedPrompt = mergePrompt(prompt!, extractedMetadata);
    } else if (originalPromptType == PromptType.MESSAGE_FOR_ASSET) {
        prompt = await getPromptDB(88); // message for asset
        mergedPrompt = mergePrompt(prompt!, extractedMetadata);
    } else if (
        openaiMessages.length >= 2 &&
        openaiMessages.slice(-2)[0].role === "user" &&
        openaiMessages.slice(-2)[1].role === "assistant" &&
        openaiMessages
            .slice(-2)[0]
            .content?.toString()
            .includes("Kuss Nachricht") == false
    ) {
        console.log("promptForGemini", originalPromptType);
        prompt = await getPromptDB(93);
        /**
         *
         * Nachricht 1 (älteste):
         * Nachricht 2:
         * Nachricht 3:
         * Nachricht 4 (neueste):
         */

        prompt!.prompt = prompt!.prompt.replace(
            "{{previousMessages}}",
            openaiMessagesToSystemMessageString(openaiMessages)
        );
        mergedPrompt = mergePrompt(prompt!, extractedMetadata);
    } else if (originalPromptType == PromptType.REACTIVATE_NEW_USER) {
        prompt = await getPromptDB(91);
        mergedPrompt = mergePrompt(prompt!, extractedMetadata);
    } else if (originalPromptType == PromptType.REACTIVATE_USER) {
        prompt = await getPromptDB(90);
        mergedPrompt = mergePrompt(prompt!, extractedMetadata);
    } else if (originalPromptType == PromptType.KISS) {
        prompt = await getPromptDB(89);
        mergedPrompt = mergePrompt(prompt!, extractedMetadata);
    } else if (
        openaiMessages.length > 0 &&
        openaiMessages.filter((m) => m.role == "user").length <= 3
    ) {
        if (
            origin == "gold" ||
            origin == "whatsmeet" ||
            origin == "xloves" ||
            origin == "myloves" ||
            origin == "onlydates69"
        ) {
            prompt = await getPromptDB(118);
            console.log("get prompt 118");
        } else {
            prompt = await getPromptDB(85); // first message
            console.log("get prompt 85");
        }
        mergedPrompt = mergePrompt(prompt!, extractedMetadata);
    } else if (
        openaiMessages.length > 1 &&
        siteInfos.metaData.customerInfo.gender == "male" &&
        siteInfos.metaData.moderatorInfo.gender == "female"
    ) {
        prompt = await getPromptDB(86); // woman talks with man
        mergedPrompt = mergePrompt(prompt!, extractedMetadata);
    } else if (
        openaiMessages.length > 1 &&
        siteInfos.metaData.customerInfo.gender == "female" &&
        siteInfos.metaData.moderatorInfo.gender == "male"
    ) {
        prompt = await getPromptDB(87); // man talks with woman
        mergedPrompt = mergePrompt(prompt!, extractedMetadata);
    } else {
        return undefined;
    }
    return { mergedPrompt, prompt };
}

// Use your API key
// Right now we only have one prompt for that. ID: 85
export async function inferenceGemini(
    extractedMetadata: ExtractedMetadata,
    computedMetadata: ComputedMetadata,
    siteInfos: SiteInfos,
    origin: PageType,
    openaiMessages: OpenAI.ChatCompletionMessageParam[],
    promptType: PromptType,
    asset?: ImageAnalysisData
) {
    if (
        promptType != PromptType.DEFAULT &&
        promptType != PromptType.KISS &&
        promptType != PromptType.MODERATOR_MALE &&
        promptType != PromptType.MESSAGE_FOR_ASSET &&
        promptType != PromptType.REACTIVATE_NEW_USER &&
        promptType != PromptType.REACTIVATE_USER
    )
        return undefined;

    const prompt = await promptForGemini(
        siteInfos,
        extractedMetadata,
        origin,
        openaiMessages,
        promptType
    );

    if (!prompt) {
        return undefined;
    }

    // Convert single config to array for consistent handling
    const modelConfigs = Array.isArray(prompt.prompt?.ai_model_config)
        ? prompt.prompt?.ai_model_config
        : [prompt.prompt?.ai_model_config];

    const credentials = await auth.getCredentials();
    let lastError: any = null;

    // Try each model config until one succeeds
    for (const config of modelConfigs) {
        console.log("gemini config", config);
        let messagesCopy = [...openaiMessages];

        if (
            config.model == "gemini-2.0-flash-001" ||
            config.model == "gemini-2.0-flash-lite-001" ||
            config.model == "gemini-2.5-flash" ||
            !config.vertexai
        ) {
            let attemptCount = 0;
            let totalDelay = 0;

            while (true) {
                if (totalDelay > 30000) {
                    console.log("Total delay exceeded 30 seconds");
                    break; // Try next config
                }

                try {
                    if (prompt.prompt?.id == 93) {
                        let userMessageForActivation = `Animiere den Kunde, weil er dir auf deine letzte Nachricht nicht mehr geantwortet hat!`;
                        userMessageForActivation = mergePrompt(
                            {
                                id: 93,
                                prompt: userMessageForActivation,
                                ai_model_config: {
                                    model: "gemini-2.0-flash-001",
                                },
                                ai_model: "gemini-2.0-flash-001",
                            },
                            extractedMetadata
                        );
                        messagesCopy = [
                            {
                                role: "user",
                                content: userMessageForActivation,
                            },
                        ];
                    }
                    const geminiRes =
                        await AIAnalysisService.generateTextFromPrompt(
                            prompt.mergedPrompt,
                            messagesCopy,
                            config.temperature,
                            config.topP,
                            config.model,
                            config.thinking
                        );

                    attemptCount++;

                    if (
                        geminiRes.success &&
                        geminiRes.data &&
                        geminiRes.data.generatedText &&
                        geminiRes.data.generatedText !== ""
                    ) {
                        if (attemptCount >= 2) {
                            console.log(
                                "gemini res not empty after",
                                attemptCount,
                                "attempts",
                                promptType
                            );
                        }

                        console.log("Gemini res", geminiRes.data.generatedText);
                        const analyticsItem = generateAnalyticsItem(
                            [
                                {
                                    content: prompt.mergedPrompt!,
                                    role: "system",
                                },
                                ...messagesCopy,
                            ],
                            siteInfos,
                            extractedMetadata,
                            computedMetadata,
                            prompt.prompt!,
                            geminiRes.data.generatedText,
                            undefined,
                            undefined,
                            asset
                        );
                        await createAnalyticsItem({ ...analyticsItem }, "");
                        return geminiRes.data.generatedText;
                    }

                    console.log(
                        "Gemini res empty - attempt",
                        attemptCount,
                        promptType,
                        messagesCopy[messagesCopy.length - 1].content
                    );

                    if (attemptCount >= 3) {
                        console.warn(
                            `Gemini 2.0 flash failed after ${attemptCount} attempts`
                        );
                        break; // Try next config
                    }

                    await new Promise((resolve) =>
                        setTimeout(resolve, 5000 * attemptCount)
                    );
                    totalDelay += 5000 * attemptCount;
                } catch (error: any) {
                    console.error(
                        "Error during Gemini 2.0 flash inference:",
                        error
                    );
                    lastError = error;
                    attemptCount++;

                    if (attemptCount >= 3) {
                        console.warn(
                            `Error from Gemini 2.0 flash after ${attemptCount} attempts:`,
                            error
                        );
                        break; // Try next config
                    }

                    await new Promise((resolve) =>
                        setTimeout(resolve, 5000 * attemptCount)
                    );
                    totalDelay += 5000 * attemptCount;
                }
            }
        } else {
            try {
                const ai = new GoogleGenAI({
                    vertexai: true,
                    project: config.project,
                    location: config.location,
                    googleAuthOptions: {
                        credentials: credentials,
                    },
                });

                // Set up generation config
                const generationConfig: GenerateContentConfig = {
                    temperature: config.temperature,
                    topP: config.topP,
                    responseModalities: ["TEXT"],
                    candidateCount: 1,
                    safetySettings: [
                        {
                            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                            threshold: HarmBlockThreshold.OFF,
                        },
                        {
                            category:
                                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                            threshold: HarmBlockThreshold.OFF,
                        },
                        {
                            category:
                                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                            threshold: HarmBlockThreshold.OFF,
                        },
                        {
                            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
                            threshold: HarmBlockThreshold.OFF,
                        },
                    ],
                    systemInstruction: {
                        parts: [{ text: prompt?.mergedPrompt }],
                    },
                };

                if (openaiMessages.length == 0) {
                    messagesCopy = [
                        {
                            role: "user",
                            content:
                                "reanimiere deinen Gesprächspartner, der dir bisher noch keine Nachricht gesendet hat",
                        },
                    ];
                } else if (
                    openaiMessages.length > 1 &&
                    openaiMessages.slice(-2)[0].role === "user" &&
                    openaiMessages.slice(-2)[1].role === "assistant"
                ) {
                    messagesCopy = [
                        {
                            role: "user",
                            content: "reanimiere deinen Gesprächspartner",
                        },
                    ];
                } else if (
                    promptType == PromptType.REACTIVATE_USER ||
                    promptType == PromptType.REACTIVATE_NEW_USER
                ) {
                    messagesCopy = [
                        {
                            role: "user",
                            content: "reanimiere den User",
                        },
                    ];
                }
                let contents = openAIMessagesToGemini(messagesCopy);

                console.log({ messagesCopy, contents });

                const req: GenerateContentParameters = {
                    model: config.model,
                    contents: contents,
                    config: generationConfig,
                };

                let res: GenerateContentResponse | undefined = undefined;
                let attemptCount = 0;
                let totalDelay = 0;
                while (true) {
                    if (totalDelay > 30000) {
                        console.log("Total delay exceeded 30 seconds");
                        break; // Try next config
                    }

                    try {
                        res = await ai.models.generateContent(req);
                        attemptCount++;

                        if (res.text && res.text !== "") {
                            if (attemptCount >= 2) {
                                console.log(
                                    "gemini res not empty after",
                                    attemptCount,
                                    "attempts",
                                    promptType
                                );
                            }

                            // Success! Create analytics and return result
                            const analyticsItem = generateAnalyticsItem(
                                [
                                    {
                                        content: prompt.mergedPrompt!,
                                        role: "system",
                                    },
                                    ...messagesCopy,
                                ],
                                siteInfos,
                                extractedMetadata,
                                computedMetadata,
                                prompt.prompt!,
                                res.text!,
                                undefined,
                                undefined,
                                asset
                            );
                            await createAnalyticsItem({ ...analyticsItem }, "");

                            return res.text;
                        }

                        console.log(
                            "Gemini res empty - attempt",
                            attemptCount,
                            promptType,
                            messagesCopy[messagesCopy.length - 1].content
                        );
                        await new Promise((resolve) =>
                            setTimeout(resolve, 5000 * attemptCount)
                        );
                        totalDelay += 5000 * attemptCount;
                    } catch (error: any) {
                        if (
                            error.toString().includes("contents are empty") ||
                            error.toString().includes("contents are required")
                        ) {
                            console.log(
                                "Gemini contents required",
                                JSON.stringify(contents, null, 2)
                            );
                            return undefined;
                        }

                        console.error("Error during inferenceGemini:", error);
                        lastError = error;
                        attemptCount++;

                        if (attemptCount >= 3) {
                            console.warn(
                                `Error from Gemini after ${attemptCount} attempts:`,
                                error
                            );
                            break; // Try next config
                        }

                        await new Promise((resolve) =>
                            setTimeout(resolve, 5000 * attemptCount)
                        );
                        totalDelay += 5000 * attemptCount;
                    }
                }
            } catch (error) {
                console.error("Error setting up Gemini instance:", error);
                lastError = error;
                continue; // Try next config
            }
        }
    }

    // If we get here, all configs failed
    console.error("All model configs failed. Last error:", lastError);
    return undefined;
}

export interface Turn {
    role: string;
    parts: { text: string }[];
}

export function openAIMessagesToGemini(
    openaiMessages: OpenAI.ChatCompletionMessageParam[]
) {
    let allMessages = openaiMessages
        .filter((m) => m.role != "system")
        .map((message) => ({
            role: message.role === "assistant" ? "model" : "user", // Standardize roles
            text: message.content as string,
        }));

    // Group consecutive messages by role using reduce
    const groupedContents = allMessages.reduce((acc, currentMessage) => {
        // Get the last turn added to the accumulator
        const lastTurn = acc.length > 0 ? acc[acc.length - 1] : null;

        if (lastTurn && lastTurn.role === currentMessage.role) {
            // If the role is the same as the last turn, add the text to its 'parts'
            lastTurn.parts.push({
                text: (currentMessage.role +
                    ": " +
                    currentMessage.text) as string,
            });
        } else {
            // If the role is different or it's the first message, create a new turn object
            acc.push({
                role: currentMessage.role,
                parts: [
                    {
                        text: (currentMessage.role +
                            ": " +
                            currentMessage.text) as string,
                    },
                ], // Start parts array with current message text
            });
        }
        return acc; // Return the updated accumulator
    }, [] as Turn[]); // Initialize with an empty array typed correctly

    return groupedContents;
}

export function openaiMessagesToSystemMessageString(
    openaiMessages: OpenAI.ChatCompletionMessageParam[]
) {
    const lastFourMessages = openaiMessages.slice(-4);

    return lastFourMessages
        .map((m, i) => {
            const isNewest = i == lastFourMessages.length - 1;
            const isOldest = i == 0;
            const isLastMessageFromAssistant =
                isNewest && m.role === "assistant";
            const isLastMessageFromUser = isNewest && m.role === "user";

            const messageInfo =
                m.role == "user"
                    ? isLastMessageFromUser
                        ? "Nachricht von deinem Gesprächspartner an dich, die noch nicht beantwortet wurde. Auf diese Nachricht sollst du antworten"
                        : "Nachricht von deinem Gesprächspartner an dich"
                    : isLastMessageFromAssistant
                    ? "Nachricht von dir (assistant), die von deinem Gesprächspartner noch nicht beantwortet wurde. Auf diese Nachricht nicht eingehen, diese dient nur als Kontext"
                    : "Nachricht von dir (assistant)";

            return `<Nachricht ${i + 1} info="${messageInfo}">${
                isOldest ? " (älteste)" : isNewest ? " (neueste)" : ""
            }: ${m.content}</Nachricht ${i + 1}>`;
        })
        .join("\n");
}
