import axios from "axios";
import { v4 as uuidv4 } from "uuid";

const CHAT_MAESTRO_URL = "https://chat-maestro.onrender.com";

interface ChatMaestroRequest {
    taskId: string;
    data: Record<string, any>;
    expiresAt: string;
    isPriority?: boolean;
}

interface ChatMaestroAuthOptions {
    /** Bearer token (JWT) for authentication */
    bearerToken?: string;
    /** API key for authentication */
    apiKey?: string;
    /** Use Supabase admin token automatically */
    useSupabaseAuth?: boolean;
}

interface ChatMaestroResponse {
    taskId: string;
    // Add other response properties as needed
}

interface TaskStatusResponse {
    taskId: string;
    status: "in queue" | "processing" | "finished" | "expired" | "failed";
    data: Record<string, any>; //has language as well
    queuedAt: string;
    processedAt?: string;
    completedAt?: string;
}

interface PollOptions {
    /** Polling interval in milliseconds (default: 2000ms) */
    intervalMs?: number;
    /** Maximum polling duration in milliseconds (default: 300000ms = 5 minutes) */
    maxDurationMs?: number;
    /** Authentication options for polling requests */
    authOptions?: ChatMaestroAuthOptions;
}

export async function sendChatMaestroRequest(
    data: Record<string, any>,
    authOptions?: ChatMaestroAuthOptions,
    isPriority?: boolean,
    startedAt?: Date
): Promise<string> {
    // Generate random UUID for task ID
    const taskId = uuidv4();
    console.log("[ChatMaestro] Starting request", { taskId, isPriority: isPriority || false });

    // Calculate expiration time (6 minutes from when the flow started, or current time if no startedAt provided)
    const baseTime = startedAt ? startedAt.getTime() : Date.now();
    const expiresAt = new Date(baseTime + 6 * 60 * 1000).toISOString();

    const requestBody: ChatMaestroRequest = {
        taskId,
        data,
        expiresAt
    };

    // Build headers with authentication
    const headers: Record<string, string> = {
        "Content-Type": "application/json",
    };

    // Add authentication headers based on options
    if (authOptions?.bearerToken) {
        headers["Authorization"] = `Bearer ${authOptions.bearerToken}`;
    } else if (authOptions?.apiKey) {
        headers["x-api-key"] = authOptions.apiKey;
    } else if (authOptions?.useSupabaseAuth) {
        headers["Authorization"] = `Bearer ${process.env.SUPABASE_SERVICE_KEY}`;
    }

    try {
        const url = isPriority ? CHAT_MAESTRO_URL+"/task/priority" : CHAT_MAESTRO_URL+"/task";

        await axios.post(url, requestBody, {
            headers,
        });

        console.log("[ChatMaestro] Request sent successfully", { taskId });
        return taskId;
    } catch (error) {
        console.error("[ChatMaestro] Request failed", {
            taskId,
            error: error instanceof Error ? error.message : String(error)
        });
        throw new Error(`Failed to send request to Chat Maestro: ${error}`);
    }

}

export async function pollChatMaestroTask(
    taskId: string,
    options?: PollOptions
): Promise<Record<string, any | null>> {
    const {
        intervalMs = 5000,
        maxDurationMs = 300000, // 5 minutes
        authOptions
    } = options || {};

    console.log("[ChatMaestro] Starting polling", { taskId });

    const startTime = Date.now();
    const pollUrl = `${CHAT_MAESTRO_URL}/task/${taskId}`;
    let pollAttempt = 0;

    // Build headers with authentication
    const headers: Record<string, string> = {
        "Content-Type": "application/json",
    };

    if (authOptions?.bearerToken) {
        headers["Authorization"] = `Bearer ${authOptions.bearerToken}`;
    } else if (authOptions?.apiKey) {
        headers["x-api-key"] = authOptions.apiKey;
    } else if (authOptions?.useSupabaseAuth) {
        headers["Authorization"] = `Bearer ${process.env.SUPABASE_SERVICE_KEY}`;
    }

    while (true) {
        pollAttempt++;
        const elapsedTime = Date.now() - startTime;

        // Check if we've exceeded the maximum polling duration
        if (elapsedTime > maxDurationMs) {
            console.warn("[ChatMaestro] Polling timeout", { taskId });
            return {
                alert: "Polling timeout: Task did not complete within 5 minutes",
            };
        }

        try {
            const response = await axios.get(pollUrl, { headers });
            const taskStatus: TaskStatusResponse = response.data;

            console.log("[ChatMaestro] Poll response", {
                taskId,
                status: taskStatus.status,
                attempt: pollAttempt
            });

            switch (taskStatus.status) {
                case "finished":
                    console.log("[ChatMaestro] Task completed", { taskId });
                    return taskStatus.data;

                case "expired":
                    console.warn("[ChatMaestro] Task expired", { taskId });
                    return {
                        ...taskStatus.data,
                        alert: "EXPIRED",
                    };

                case "failed":
                    console.error("[ChatMaestro] Task failed", { taskId });
                    return {
                        ...taskStatus.data,
                        alert: "FAILED",
                    };

                case "in queue":
                case "processing":
                    // Continue polling - no log needed for each attempt
                    break;

                default:
                    console.warn("[ChatMaestro] Unknown status", {
                        taskId,
                        status: taskStatus.status
                    });
                    break;
            }

        } catch (error) {
            console.error("[ChatMaestro] Polling error", {
                taskId,
                error: error instanceof Error ? error.message : String(error)
            });

            // If it's an axios error, check if it's a 404 (task not found)
            if (axios.isAxiosError(error) && error.response?.status === 404) {
                return {
                    alert: "NOT_FOUND",
                };
            }
            // For other errors, return error (don't continue polling for safety)
            return {
                alert: "POLLING_ERROR",
            };
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, intervalMs));
    }
}

