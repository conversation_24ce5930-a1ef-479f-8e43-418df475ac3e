import OpenAI from "openai";
import { handleInferenceChatCompletion } from "../inference/inferenceChatComplete";
import Prompt from "../../models/Pompt";
import { run } from "node:test";
import { ExtractedMetadata, PageType } from "../../models/SiteInfos";
import { AIAnalysisService } from "../inference/gemini";
import { getPromptDB } from "../supabase/promptConfig";
import { createAnalyticsItem } from "../supabase/chatCompleteAnalyticsService";
import { mergePrompt } from "../inference/mergePrompt";

// Define the function to check the summary
function cleanupSummary(
    input: string | null
):
    | { assistant: Record<string, string>; user: Record<string, string> }
    | undefined {
    if (input == null) {
        return undefined;
    }
    // Split the input string by sections, assuming '###' is the section divider
    const sections = input
        .split("###")
        .map((section) => section.trim())
        .filter((section) => section !== "");

    if (sections.length !== 2) {
        return undefined;
    }

    // Prepare an object to hold the results
    let result = {
        assistant: {},
        user: {},
    };

    // Process each section
    sections.forEach((section) => {
        // Split the header from the data
        const lines = section.split("\n");
        const header = lines[0].toLowerCase(); // Get 'assistant' or 'user' from the first line
        const dataLines = lines.slice(1); // All other lines are data lines

        // Prepare a temporary object for this section
        let sectionData: Record<string, string> = {};

        // Process each data line
        dataLines.forEach((line) => {
            const parts = line.split(":");
            if (parts.length < 2) {
                // Skip lines that don't have a colon (no key-value pair)
                return;
            }

            let [key, value] = parts.map((part) =>
                part.trim().replace(/^-/, "")
            );

            // Skip if the key or value is malformed/empty
            if (!key || !value) {
                return;
            }

            // Add to the section object if value is not "unbekannt"
            if (value.toLowerCase().includes("unbekannt") == false) {
                // Remove duplicates and trailing commas
                sectionData[key] = [
                    ...new Set(value.split(",").map((item) => item.trim())),
                ]
                    .filter((item) => item !== "")
                    .join(", ");
            }
        });

        // Add the processed section data to the result under the correct header
        if (header.includes("assistant")) {
            result.assistant = sectionData;
        } else if (header.includes("user")) {
            result.user = sectionData;
        } else {
            throw new Error("Invalid header: " + header);
        }
    });

    return result;
}

export async function createSummary(
    data: {
        messages: (
            | OpenAI.Chat.Completions.ChatCompletionSystemMessageParam
            | OpenAI.Chat.Completions.ChatCompletionUserMessageParam
            | OpenAI.Chat.Completions.ChatCompletionAssistantMessageParam
            | OpenAI.Chat.Completions.ChatCompletionToolMessageParam
            | OpenAI.Chat.Completions.ChatCompletionFunctionMessageParam
            | any
        )[];
    },
    prompt: Prompt,
    origin: PageType,
    customerName?: string,
    moderatorName?: string,
    extractedMetadata?: ExtractedMetadata,
    runCount: number = 0
): Promise<{
    summary: {
        assistant: Record<string, string>;
        user: Record<string, string>;
    };
    resText: string;
    runCount: number;
}> {
    const response = await handleInferenceChatCompletion(
        data.messages,
        prompt.ai_model_config,
        origin
    );

    // runs in bg for now
    const promptDBGeminiNew = await getPromptDB(109);
    const testSummaryMessages = data.messages.filter((m) => m.role != "system");

    if (promptDBGeminiNew) {
        try {
            const mergedPrompt = mergePrompt(
                promptDBGeminiNew,
                extractedMetadata!
            );
            const testSummaryMessageInOneMessage: OpenAI.ChatCompletionMessageParam[] =
                [
                    {
                        role: "user",
                        content:
                            testSummaryMessages
                                .map(
                                    (m) =>
                                        m.role.replace("assistant", "model") +
                                        ": " +
                                        m.content
                                )
                                .join("\n\n") +
                            "\n\nExtrahiere die Informationen aus der Konversation",
                    },
                ];

            const responseUser = await AIAnalysisService.generateTextFromPrompt(
                mergedPrompt,
                testSummaryMessageInOneMessage,
                promptDBGeminiNew.ai_model_config.temperature,
                promptDBGeminiNew.ai_model_config.top_p,
                "gemini-2.0-flash-001"
            );

            if (!responseUser.success) {
                throw new Error("Failed to create summary user");
            }

            await createAnalyticsItem({
                chat_complete_prompt_id: promptDBGeminiNew.id!,
                prev_messages: [
                    { role: "system", content: mergedPrompt },
                    ...testSummaryMessageInOneMessage,
                ],
                origin_website: origin,
                language: "de",
                ai_generated_message: responseUser.data!.generatedText,
                ai_model: promptDBGeminiNew.ai_model_config.model,
                metadata: {
                    temperature: promptDBGeminiNew.ai_model_config.temperature,
                    top_p: promptDBGeminiNew.ai_model_config.top_p,
                    moderatorNotes: extractedMetadata?.moderatorNotes,
                },
            });
        } catch (error) {
            console.error("Error creating summary", error);
        }
    }

    console.log("gemini summary response");

    if (!response) {
        throw new Error("No response from inference");
    }

    const { content, thinkingContent } = response;

    if (!content) {
        throw new Error("No content from inference");
    }

    const summary = cleanupSummary(content);

    if (summary == undefined) {
        if (runCount > 3) {
            throw new Error("Error creating summary. Third try failed.");
        } else {
            console.log(
                "Error creating summary. Trying again. Try number: ",
                runCount + 1
            );
            return await createSummary(
                data,
                prompt,
                origin,
                customerName,
                moderatorName,
                extractedMetadata,
                runCount + 1
            );
        }
    }

    // Remove values longer than 140 characters from summary
    for (const key in summary.user) {
        if (summary.user[key] && summary.user[key].length > 140) {
            delete summary.user[key];
        }
    }

    for (const key in summary.assistant) {
        if (summary.assistant[key] && summary.assistant[key].length > 140) {
            delete summary.assistant[key];
        }
    }

    if (
        summary.user.Name &&
        summary.assistant.Name &&
        summary.user.Name === summary.assistant.Name
    ) {
        delete summary.user.Name;
        delete summary.assistant.Name;
    }

    if (
        summary.user.Name &&
        moderatorName &&
        summary.user.Name.toLowerCase() === moderatorName.toLowerCase()
    ) {
        delete summary.user.Name;
    }

    if (
        summary.assistant.Name &&
        customerName &&
        summary.assistant.Name.toLowerCase() === customerName.toLowerCase()
    ) {
        delete summary.assistant.Name;
    }

    return { summary, resText: content, runCount };
}
