import { Groq } from "groq-sdk";
import { retryGroqCall } from "../apiUtils/retryInference";

import dotenv from "dotenv";
dotenv.config();

const prompt =
    "Du bist ein Übersetzer für einen sexuellen Chatbot. Übersetze die Nachricht auf kastilianisches Spanisch. Ändere nichts am Inhalt, sondern übesetze nur! Dein Schreibstil ist jung und sexy.";
export async function translate(text: string, targetLanguage: string) {
    const messages: { role: string; content: string }[] = [
        {
            role: "system",
            content: prompt,
        },
        { role: "user", content: text },
    ];
    const groq = new Groq({
        apiKey: process.env.GROQ_API_KEY,
    });

    const chatCompletion = await retryGroqCall<any>(() =>
        groq.chat.completions.create({
            model: "llama3-70b-8192",
            top_p: 1,
            stream: false,
            max_tokens: 1024,
            temperature: 1,
            messages: messages.map((msg) => ({
                role: msg.role as "system" | "user" | "assistant",
                content: msg.content,
            })),
        })
    );

    const result = chatCompletion.choices[0]?.message?.content;

    return result;
}
