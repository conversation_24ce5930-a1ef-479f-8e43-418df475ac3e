import TelegramBot from "node-telegram-bot-api";
import { getMacDB } from "../supabase/macs";
import { requestRestart, requestRestartSplashtop } from "../../routes/mac";

const TELEGRAM_TOKEN = process.env.TELEGRAM_BOT_TOKEN;
const ALLOWED_CHAT_ID = "-4214641327";

export function initializeTelegramBot() {
    if (process.env.ENVIRONMENT === "development") return;

    let bot: TelegramBot | null = null;
    try {
        bot = new TelegramBot(TELEGRAM_TOKEN!, {
            polling: {
                autoStart: true,
                params: {
                    timeout: 10,
                },
            },
        });

        setupPollingErrorHandler(bot);
        setupMessageHandler(bot);
    } catch (error) {
        console.error("Failed to initialize Telegram bot:", error);
    }
}

function setupPollingErrorHandler(bot: TelegramBot) {
    bot.on("polling_error", () => {
        if (bot) {
            setTimeout(() => {
                try {
                    bot.stopPolling();
                    setTimeout(() => {
                        bot.startPolling();
                    }, 1000);
                } catch (e) {
                    console.error("Error restarting polling:", e);
                }
            }, 5000);
        }
    });
}

function setupMessageHandler(bot: TelegramBot) {
    bot.on("message", async (msg) => {
        if (msg.chat.id.toString() !== ALLOWED_CHAT_ID) return;
        if (!msg.text) return;

        const text = msg.text.toLowerCase();

        const match = text.match(
            /(?:restart\s+mac[-\s]?(\d+)|mac[-\s]?(\d+)\s+restart)/
        );

        const splashtopMatch = text.match(
            /(?:restart\s+splashtop\s+mac[-\s]?(\d+)|splashtop\s+mac[-\s]?(\d+)\s+restart)/
        );

        if (splashtopMatch) {
            await handleSplashtopRestart(bot, msg, splashtopMatch);
            return;
        }

        if (match) {
            await handleMacRestart(bot, msg, match);
        }
    });
}

async function handleSplashtopRestart(
    bot: TelegramBot,
    msg: TelegramBot.Message,
    match: RegExpMatchArray
) {
    const macNumber = match[1] || match[2];
    const hostname = `mac${macNumber}`;

    bot.sendMessage(msg.chat.id, `🔄 Restarting Splashtop on ${hostname}...`);

    try {
        await requestRestartSplashtop(hostname);
    } catch (error) {
        bot.sendMessage(
            msg.chat.id,
            `❌ Error: ${hostname} not found or not online. Make sure it's running the restart script.`
        );
        return;
    }

    setTimeout(async () => {
        const mac = await getMacDB(hostname);
        if (mac?.shouldRestartSplashtop) {
            bot.sendMessage(
                msg.chat.id,
                `⚠️ Warning: ${hostname} hasn't responded to restart splashtop request. Check if it's online.`
            );
        } else {
            bot.sendMessage(
                msg.chat.id,
                `✅ ${hostname} restarted Splashtop successfully.`
            );
        }
    }, 90000);
}

async function handleMacRestart(
    bot: TelegramBot,
    msg: TelegramBot.Message,
    match: RegExpMatchArray
) {
    const macNumber = match[1] || match[2];
    const hostname = `mac${macNumber}`;

    bot.sendMessage(msg.chat.id, `🔄 Restarting ${hostname}...`);

    try {
        await requestRestart(hostname);
    } catch (error) {
        bot.sendMessage(
            msg.chat.id,
            `❌ Error: ${hostname} not found or not online. Make sure it's running the restart script.`
        );
        return;
    }

    setTimeout(async () => {
        const mac = await getMacDB(hostname);
        if (mac?.shouldRestart) {
            bot.sendMessage(
                msg.chat.id,
                `⚠️ Warning: ${hostname} hasn't responded to restart request. Check if it's online.`
            );
        } else {
            bot.sendMessage(
                msg.chat.id,
                `✅ ${hostname} restarted successfully.`
            );
        }
    }, 90000);
}
