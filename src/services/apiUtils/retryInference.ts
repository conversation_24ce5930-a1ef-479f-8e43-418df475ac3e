/**
 * Utility functions for handling Groq API calls with retry logic
 */

/**
 * Executes a function that calls the Groq API with retry logic for rate limits
 *
 * @param fnToCall - Function that returns a Promise for the Groq API call
 * @param maxRetries - Maximum number of retries before giving up (default: 3)
 * @param delayMs - Delay between retries in milliseconds (default: 3000)
 * @returns Promise resolving to the result of the function call
 */
export async function retryGroqCall<T>(
    fnToCall: () => Promise<T>,
    maxRetries: number = 2
): Promise<T> {
    let retries = 0;
    let delayMs = Math.floor(Math.random() * (120000 - 60000) + 60000); // Random delay between 1-2 minutes
    while (true) {
        try {
            return await fnToCall();
        } catch (error: any) {
            // Check if it's a rate limit error (typically 429 Too Many Requests)
            const isRateLimit =
                error?.status === 429 ||
                error?.message?.includes("rate limit") ||
                error?.message?.includes("too many requests");

            if (isRateLimit && retries < maxRetries) {
                retries++;
                await new Promise((resolve) => setTimeout(resolve, delayMs));
                continue;
            }

            // If it's not a rate limit error or we've exceeded max retries, rethrow
            throw error;
        }
    }
}

export async function retryInference(fnToCall: () => Promise<any>) {
    let retries = 0;
    let delayMs = Math.floor(Math.random() * (10000 - 5000) + 5000); // Random delay between 5-10 seconds
    while (true) {
        try {
            const res = await fnToCall();
            if (res == null) {
                retries++;
                await new Promise((resolve) => setTimeout(resolve, delayMs));
                continue;
            }
            return res;
        } catch (error: any) {
            if (
                error?.status === 429 ||
                error?.message?.includes("rate limit") ||
                error?.message?.includes("too many requests")
            ) {
                retries++;
                await new Promise((resolve) => setTimeout(resolve, delayMs));
                continue;
            }
            throw error;
        }
    }
}
