// Define the PromptType enum
export enum PromptType {
    NO_PREVIOUS_MESSAGES = "no_previous_messages",
    KLAPS = "klaps",
    KISS = "kiss",
    REACTIVATE_USER = "reactivate_user",
    REACTIVATE_NEW_USER = "reactivate_new_user",
    OUTPUT_CONTROL = "outputControl",
    DEFAULT = "default",
    DEFAULT_MODERATOR_MALE_USER_MALE = "default_moderator_male_user_male",
    DEFAULT_MODERATOR_FEMALE_USER_FEMALE = "default_moderator_female_user_female",
    MODERATOR_MALE = "moderator_male",
    SUMMARY = "summary",
    FRIEND_REQUEST = "friend_request",
    MESSAGE_FOR_ASSET = "message_for_asset",
    LONGTERM_CUSTOMER = "longterm_customer",
    LONGTERM_CUSTOMER_FEMALE = "longterm_customer_female",
    ACTIVATION_MESSAGE = "activation_message",
    FRIEND_REQUEST_ACCEPTED = "friend_request_accepted",
}

import dayjs from "dayjs";
import { ExtractedMetadata, SiteInfos } from "../../models/SiteInfos";

export function getPromptTypeChatCompletion(siteInfos: SiteInfos): PromptType {
    const { origin } = siteInfos;

    if (origin == "cw") {
        return getPromptTypeCW(siteInfos);
    } else {
        return getGenericPromptType(siteInfos);
    }
}

const SLAP_MESSAGES_CW = ["[Klaps]", "[Slap]", "[Klaps/Slap]"];
const KISS_MESSAGES_CW = ["[Kuss]", "[Kiss]", "Du hast einen Kuss erhalten!"];
const REACTIVATION_MESSAGES_CW = [
    "[Bitte User reaktivieren!]",
    "[Please reactivate the user!]",
];
const ALERT_BOX_REACTIVATION_MESSAGES = [
    "reaktiviere",
    "reactivate",
    "asa dialog",
];

interface PromptTypeConfig {
    reactivationMessages?: string[];
    kissMessages?: string[];
}

function getGenericPromptType(
    siteInfos: SiteInfos,
    config: PromptTypeConfig = {}
): PromptType {
    const { messages, metaData } = siteInfos;
    if (messages.length == 0) {
        return PromptType.ACTIVATION_MESSAGE;
    }

    if (siteInfos.origin == "avz") {
        return PromptType.DEFAULT;
    }

    // Handle empty messages
    if (messages.length === 0) {
        return PromptType.REACTIVATE_NEW_USER;
    }

    const lastMessage = messages[messages.length - 1];

    if (lastMessage.text == undefined) {
        lastMessage.text = "";
    }

    const friendRequestIdentifiers = [
        "Freundschaftsanfragen",
        "Freundschaftsanfrage",
        "Friend request",
        "friend request",
        "*FREUNDSCHAFTSANFRAGE GESENDET*",
    ];
    if (
        friendRequestIdentifiers.includes(lastMessage.text.trim()) &&
        siteInfos.origin != "justlo"
    ) {
        return PromptType.FRIEND_REQUEST;
    }

    // only in justlo for now
    if (lastMessage.text.includes("*** Freundschaftsanfrage angenommen ***")) {
        return PromptType.FRIEND_REQUEST_ACCEPTED;
    }

    if (config.reactivationMessages?.includes(lastMessage.text)) {
        // Check for specific reactivation messages (used in CW)
        if (
            metaData.sessionStart &&
            dayjs().diff(metaData.sessionStart, "day") <= 7
        ) {
            return PromptType.REACTIVATE_NEW_USER;
        }
        return PromptType.REACTIVATE_USER;
    }

    // Check for kiss messages (used in Gold)
    // if (
    //     config.kissMessages?.some((msg) => lastMessage.text.includes(msg)) &&
    //     metaData.ins === 1
    // ) {
    //     return PromptType.KISS;
    // }

    // Handle reactivation cases
    if (lastMessage.type === "sent") {
        const isNewUser =
            // Check ins count if available
            (metaData.ins && metaData.ins < 10) ||
            // Check message count
            messages.length <= 2 ||
            // Check session start date
            (metaData.sessionStart &&
                dayjs().diff(metaData.sessionStart, "day") <= 7);

        return isNewUser
            ? PromptType.REACTIVATE_NEW_USER
            : PromptType.REACTIVATE_USER;
    }

    if (messages.filter((m) => m.type == "received").length == 1) {
        const lastUserMessage = messages.filter((m) => m.type == "received")[0]
            .text;

        if (lastUserMessage.toLowerCase() == "kuss nachricht!💋") {
            return PromptType.KISS;
        }
    }

    // Check ins count if available
    const isLongtermCustomer = metaData.ins && metaData.ins >= 100;
    // Check session start date
    // (metaData.sessionStart &&
    //     !metaData.ins &&
    //     dayjs().diff(metaData.sessionStart, "day") >= 14);

    if (isLongtermCustomer) {
        return metaData.customerInfo.gender == "female"
            ? PromptType.LONGTERM_CUSTOMER_FEMALE
            : PromptType.LONGTERM_CUSTOMER;
    }

    // Handle gender-based prompt types
    const { customerInfo, moderatorInfo } = metaData;

    if (customerInfo.gender === "male" && moderatorInfo.gender === "male") {
        return PromptType.DEFAULT_MODERATOR_MALE_USER_MALE;
    }

    if (customerInfo.gender === "female" && moderatorInfo.gender === "female") {
        return PromptType.DEFAULT_MODERATOR_FEMALE_USER_FEMALE;
    }

    if (customerInfo.gender === "female" && moderatorInfo.gender === "male") {
        return PromptType.MODERATOR_MALE;
    }

    return PromptType.DEFAULT;
}

// Simplified site-specific functions
function getPromptTypeCW(siteInfos: SiteInfos): PromptType {
    return getGenericPromptType(siteInfos, {
        reactivationMessages: REACTIVATION_MESSAGES_CW,
    });
}
