import axios from "axios";
import supabaseAdminClient from "../../util/createClient";
import fs from "fs";
import path from "path";
import csv from "csv-parse/sync";
// Remove the direct require and use a variable instead
let postalCodeData: PostalCodeCity[] = [];

// Define interface for postal code data
interface PostalCodeCity {
    postalCode: string;
    cityName: string;
    postalCodePrefix: string;
}

export interface Location {
    lat: number;
    lng: number;
}

interface CityData {
    Name: string;
    Lat: number | string;
    Long: number | string;
    [key: string]: any;
}

export async function getCityCoordinates(
    city?: string,
    country: "DE" | "AT" | "CH" = "DE"
): Promise<Location | undefined> {
    if (!city) {
        return undefined;
    }

    const supabase = supabaseAdminClient();

    let processedCity = city
        .split(",")
        .map((part) => part.trim())
        .join(", ");

    try {
        // Search for the exact city name in the Supabase database
        const { data, error } = await supabase
            .from("cities")
            .select("*")
            .eq("name", processedCity)
            .limit(1);

        if (error) {
            throw new Error("Error querying Supabase database");
        }

        if (data && data.length > 0) {
            return {
                lat: data[0].lat,
                lng: data[0].lng,
            };
        }

        // If city not found in Supabase, fall back to Google API
        try {
            const googleResult = await getGoogleCityCoordinates(
                processedCity,
                country
            );

            if (!googleResult) {
                return undefined;
            }

            // if the city name contains a number or "str", return the google result and dont insert it into the database
            if (/\d/.test(processedCity) || processedCity.includes("str")) {
                return googleResult;
            }

            // Insert the new city into the Supabase database
            const { data: insertData, error: insertError } = await supabase
                .from("cities")
                .insert({
                    name: processedCity,
                    lat: googleResult.lat,
                    lng: googleResult.lng,
                    location: `POINT(${googleResult.lng} ${googleResult.lat})`,
                    country: country,
                    use_as_nearby_city: false,
                });

            if (insertError) {
                console.log(
                    "ERROR: Failed to insert new city into Supabase",
                    insertError
                );
            }

            return googleResult;
        } catch (error) {
            console.error(
                "Error in getCityCoordinates. Probably center of germany",
                processedCity,
                country
            );
            return undefined;
        }
    } catch (error) {
        console.error("Error in getCityCoordinates:", error);
        throw error;
    }
}

async function getGoogleCityCoordinates(
    city: string,
    country: "DE" | "AT" | "CH" = "DE"
): Promise<Location | undefined> {
    const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;
    let googleCountry = () => {
        if (country === "DE") {
            return "germany";
        } else if (country === "AT") {
            return "austria";
        } else if (country === "CH") {
            return "switzerland";
        }
    };
    const geocodeUrl = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(
        city + ", " + googleCountry()
    )}&key=${GOOGLE_API_KEY}`;

    try {
        const response = await axios.get(geocodeUrl);
        if (response.data.status === "OK" && response.data.results.length > 0) {
            const result = response.data.results[0];
            const location = result.geometry.location;
            const isInDach = result.address_components.some(
                (component: any) =>
                    (component.short_name === "DE" ||
                        component.short_name === "AT" ||
                        component.short_name === "CH") &&
                    component.types.includes("country")
            );

            if (!isInDach) {
                throw new Error(
                    "The specified city is not in Germany, Austria, or Switzerland."
                );
            }

            if (location.lat === 51.165691 && location.lng === 10.451526) {
                throw new Error(
                    "City not found in Google API. center of germany, city: " +
                        city +
                        ", country: " +
                        country
                );
            }

            return location;
        } else {
            console.error("City not found in Google API", city, country);
            return undefined;
        }
    } catch (error) {
        console.error("Error in Google API request:", error);
        return undefined;
    }
}

// KD-Tree implementation for spatial indexing
class KDNode {
    city: CityData;
    left: KDNode | null = null;
    right: KDNode | null = null;

    constructor(city: CityData) {
        this.city = city;
    }
}

class KDTree {
    root: KDNode | null = null;

    // Build KD-Tree from cities array
    build(cities: CityData[]): void {
        this.root = this.buildTree(cities, 0);
    }

    private buildTree(cities: CityData[], depth: number): KDNode | null {
        if (cities.length === 0) return null;

        // Sort based on latitude (even depth) or longitude (odd depth)
        const axis = depth % 2;
        const sortKey = axis === 0 ? "Lat" : "Long";

        cities.sort(
            (a, b) =>
                parseFloat(a[sortKey].toString()) -
                parseFloat(b[sortKey].toString())
        );

        // Select median as pivot
        const medianIndex = Math.floor(cities.length / 2);
        const node = new KDNode(cities[medianIndex]);

        // Recursively build left and right subtrees
        node.left = this.buildTree(cities.slice(0, medianIndex), depth + 1);
        node.right = this.buildTree(cities.slice(medianIndex + 1), depth + 1);

        return node;
    }

    // Find nearest city to the given location
    findNearest(location: Location): CityData | null {
        if (!this.root) return null;

        let nearestCity: CityData | null = null;
        let minDistance = Infinity;

        this.search(this.root, location, 0, (city, distance) => {
            if (distance < minDistance) {
                minDistance = distance;
                nearestCity = city;
            }
        });

        return nearestCity;
    }

    private search(
        node: KDNode | null,
        location: Location,
        depth: number,
        callback: (city: CityData, distance: number) => void
    ): void {
        if (!node) return;

        // Calculate distance to current node
        const cityLat = parseFloat(node.city.Lat.toString());
        const cityLong = parseFloat(node.city.Long.toString());
        const distance = calculateDistance(
            location.lat,
            location.lng,
            cityLat,
            cityLong
        );

        // Process current node
        callback(node.city, distance);

        // Determine which subtree to search first
        const axis = depth % 2;
        const dimension = axis === 0 ? "lat" : "lng";
        const cityValue = axis === 0 ? cityLat : cityLong;
        const locationValue = location[dimension];

        const firstBranch = locationValue < cityValue ? node.left : node.right;
        const secondBranch = locationValue < cityValue ? node.right : node.left;

        // Search the more promising branch first
        this.search(firstBranch, location, depth + 1, callback);

        // Check if we need to search the other branch
        const axisDistance = Math.abs(locationValue - cityValue);
        if (axisDistance * axisDistance < distance) {
            this.search(secondBranch, location, depth + 1, callback);
        }
    }

    // New method to find a city within specified radius
    findCityWithinRadius(location: Location, radius: number): CityData | null {
        if (!this.root) return null;

        let citiesInRadius: { city: CityData; distance: number }[] = [];
        let nearestCity: CityData | null = null;
        let minDistance = Infinity;

        this.search(this.root, location, 0, (city, distance) => {
            if (distance <= radius) {
                citiesInRadius.push({ city, distance });
            }
            if (distance < minDistance) {
                minDistance = distance;
                nearestCity = city;
            }
        });

        // If we found cities within radius, return a random one
        if (citiesInRadius.length > 0) {
            const randomIndex = Math.floor(
                Math.random() * citiesInRadius.length
            );
            return citiesInRadius[randomIndex].city;
        }

        // Fall back to nearest city if none found within radius
        return nearestCity;
    }
}

// Cached data and index
let citiesKDTree: KDTree | null = null;
let citiesLoaded = false;

// Initialize the city data and spatial index
async function initializeCityData(): Promise<void> {
    if (citiesLoaded) return;

    try {
        const csvFilePath = path.join(__dirname, "citiesToSearch.csv");
        const fileContent = await fs.promises.readFile(csvFilePath, "utf-8");

        // Parse the CSV file
        const cities: CityData[] = csv.parse(fileContent, {
            columns: true,
            skip_empty_lines: true,
        });

        // Convert string coordinates to numbers
        cities.forEach((city) => {
            city.Lat = parseFloat(city.Lat.toString());
            city.Long = parseFloat(city.Long.toString());
        });

        // Build the KD-Tree
        citiesKDTree = new KDTree();
        citiesKDTree.build(cities);
        citiesLoaded = true;
    } catch (error) {
        console.error("Error initializing city data:", error);
        throw error;
    }
}

// Haversine formula for calculating distance between coordinates
function calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
): number {
    const R = 6371; // Earth's radius in km
    const dLat = toRad(lat2 - lat1);
    const dLon = toRad(lon2 - lon1);
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(toRad(lat1)) *
            Math.cos(toRad(lat2)) *
            Math.sin(dLon / 2) *
            Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
}

function toRad(degrees: number): number {
    return degrees * (Math.PI / 180);
}

// Optimized function to find nearby city
export async function findNearbyCity(
    cityLocation: Location
): Promise<{ name: string; lat: number; long: number } | undefined> {
    try {
        if (!citiesLoaded) {
            await initializeCityData();
        }

        if (!citiesKDTree) {
            console.error("City data not properly initialized");
            return undefined;
        }

        // Find a city within 30km radius, falling back to nearest if none found
        const city = citiesKDTree.findCityWithinRadius(cityLocation, 30);

        if (!city) {
            return undefined;
        }

        return {
            name: city.Name,
            lat: parseFloat(city.Lat.toString()),
            long: parseFloat(city.Long.toString()),
        };
    } catch (error) {
        console.error("Error finding nearby city:", error);
        return undefined;
    }
}

// Initialize the city data at module load time
initializeCityData().catch((error) => {
    console.error("Failed to initialize city data:", error);
});

// Initialize the postal code data at module load time
async function initializePostalCodeData(): Promise<void> {
    try {
        // Load the postal code data from the JSON file
        postalCodeData = require("./cities_plz.json");
        console.log(
            `Initialized postal code data with ${postalCodeData.length} entries`
        );
    } catch (error) {
        console.error("Error initializing postal code data:", error);
        throw error;
    }
}

// Function to get a random city from a postal code prefix
export async function getCityByPostalCodePrefix(
    postalCodePrefix: string
): Promise<{ name: string } | undefined> {
    try {
        // Make sure postal code data is initialized
        if (postalCodeData.length === 0) {
            await initializePostalCodeData();
        }

        // Filter cities by postal code prefix
        const citiesWithPrefix = postalCodeData.filter(
            (city: PostalCodeCity) => city.postalCodePrefix === postalCodePrefix
        );

        if (citiesWithPrefix.length === 0) {
            console.log(
                `No cities found for postal code prefix: ${postalCodePrefix}`
            );
            return undefined;
        }

        // Select a random city from the filtered list
        const randomIndex = Math.floor(Math.random() * citiesWithPrefix.length);
        const selectedCity = citiesWithPrefix[randomIndex];

        return {
            name: selectedCity.cityName,
        };
    } catch (error) {
        console.error("Error in getCityByPostalCodePrefix:", error);
        return undefined;
    }
}

export async function getNearbyCityPostalCodePrefix(
    postalCode: string
): Promise<{ name: string } | undefined> {
    // Extract the first two digits of the postal code
    const postalCodePrefix = postalCode.substring(0, 2);

    // Get a random city from that postal code prefix
    return await getCityByPostalCodePrefix(postalCodePrefix);
}

// Initialize both city data and postal code data at module load time
Promise.all([initializeCityData(), initializePostalCodeData()]).catch(
    (error) => {
        console.error("Failed to initialize data:", error);
    }
);
