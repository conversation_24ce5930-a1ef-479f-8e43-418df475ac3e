import Prompt from "../../models/Pompt";
import PromptConfig from "../../models/PromptConfig";
import { PageType } from "../../models/SiteInfos";
import supabaseAdminClient from "../../util/createClient";

export async function getPromptConfigDB({
    origin,
    id,
}: {
    origin?: PageType;
    id?: number;
}): Promise<PromptConfig | null> {
    let promptConfigId = id;

    if (origin == "cw") {
        promptConfigId = 1;
    } else if (
        origin == "gold" ||
        origin == "lacarna" ||
        origin == "myloves" ||
        origin == "onlydates69" ||
        origin == "xloves" ||
        origin == "whatsmeet"
    ) {
        promptConfigId = 2;
    } else if (origin == "avz") {
        promptConfigId = 3;
    } else if (origin == "b3") {
        promptConfigId = 4;
    } else if (origin == "wifu") {
        promptConfigId = 5;
    } else if (origin == "df") {
        promptConfigId = 6;
    } else if (origin == "flirtking") {
        promptConfigId = 7;
    } else if (origin == "fpc") {
        promptConfigId = 8;
    } else if (origin == "teddy") {
        promptConfigId = 9;
    } else if (origin == "kizzle") {
        promptConfigId = 10;
    } else if (origin == "route66") {
        promptConfigId = 11;
    } else if (origin == "livecreator") {
        promptConfigId = 12;
    } else if (origin == "torchwood") {
        promptConfigId = 13;
    } else if (origin == "single-jungle") {
        promptConfigId = 14;
    } else if (origin == "justlo") {
        promptConfigId = 16;
    } else {
        promptConfigId = 2;
    }

    const { data, error } = await supabaseAdminClient()
        .from("chat_complete_prompt_configs")
        .select("*")
        .eq("id", promptConfigId)
        .single();
    if (error) {
        console.error("promptconfig error", error);
        return null;
    }

    return data;
}

export async function getPromptDB(id: number): Promise<Prompt | null> {
    const { data, error } = await supabaseAdminClient()
        .from("chat_complete_prompts")
        .select("*")
        .eq("id", id)
        .single();

    if (error) {
        return null;
    }

    return data;
}
