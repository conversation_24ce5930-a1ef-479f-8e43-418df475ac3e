import supabaseAdminClient from "../../util/createClient";

export async function saveImage(imageSrc: string) {
    const supabase = supabaseAdminClient();

    // Step 1: Fetch the image from the URL
    const response = await fetch(imageSrc);
    if (!response.ok) {
        throw new Error(`Failed to fetch the image: ${response.statusText}`);
    }
    const imageBuffer = await response.blob(); // Use .buffer() for Node.js

    // Step 2: Upload the image to Supabase Storage
    const fileExt = imageSrc.split(".").pop();
    const fileName = `uploaded_image.${fileExt}`; // Consider a unique naming strategy
    const filePath = `images/${fileName}`;

    const { data, error } = await supabase.storage
        .from("chat_images") // Replace with your actual bucket name
        .upload(filePath, imageBuffer, {
            contentType: `image/${fileExt}`, // Set MIME type based on file extension
            upsert: false, // Set to true if you want to overwrite existing files
        });

    if (error) {
        throw new Error(`Failed to upload image to storage: ${error.message}`);
    }

    // Optionally, return the file path or public URL if needed
    return {
        path: filePath,
        publicURL: supabase.storage
            .from("your-bucket-name")
            .getPublicUrl(filePath).data.publicUrl,
    };
}
