import supabaseAdminClient from "../../util/createClient";

export interface ChatUser {
    id: number;
    origin: string;
    username: string;
    password: string;
    url: string[];
    created_at: string;
    ip_address: string;
    age: number;
    adjective: string[];
    active_time: any[];
    max_hours: number;
    active_start_time: string;
    active_end_time: string;
    state: string;
}

export async function getChatUsersByOrigin(
    origin: string
): Promise<ChatUser[]> {
    const supabase = supabaseAdminClient();

    const { data, error } = await supabase
        .from("chat_users")
        .select("*")
        .eq("origin", origin);

    if (error) {
        console.error("Error fetching chat users by origin:", error);
        return [];
    }

    return data || [];
}
