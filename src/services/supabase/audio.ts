import { randomUUID } from "crypto";
import AgentAudioAnalyticsItem from "../../models/AgentAudioAnalytics";
import { DeeplSupportedLanguage } from "../../models/DeeplSupportedLanguages";
import supabaseAdminClient from "../../util/createClient";

// service to save audio files and transcriptions so we can use them later
export async function saveAudioFile(
    audioData: any,
    transcribedText: string,
    language: DeeplSupportedLanguage,
    agentId: string
) {
    const uuid = randomUUID();
    const { error } = await supabaseAdminClient()
        .storage.from("transcription_audios")
        .upload("agents/" + agentId + "/" + uuid + ".mp3", audioData, {
            contentType: "audio/mpeg",
        });

    if (error) return;

    const agentAudioAnalyticsItem: AgentAudioAnalyticsItem = {
        id: uuid,
        transcribed_text: transcribedText,
        language: language.toLowerCase(),
        agent_id: agentId,
    };

    await supabaseAdminClient()
        .from("agent_audio_analytics")
        .insert(agentAudioAnalyticsItem)
        .select()
        .single();
}
