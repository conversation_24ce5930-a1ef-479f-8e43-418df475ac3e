import { UserResponse } from "@supabase/supabase-js";
import supabaseAdminClient from "../../util/createClient";

export function getUserById(userId: string): Promise<UserResponse> {
    return supabaseAdminClient().auth.admin.getUserById(userId);
}

export function getUserByToken(token: string): Promise<UserResponse> {
    return supabaseAdminClient().auth.getUser(token);
}

export async function getRoleByToken(token?: string) {
    if (!token) {
        return undefined;
    }

    const user = await getUserByToken(token);

    const { data, error } = await supabaseAdminClient()
        .from("roles")
        .select("*")
        .eq("id", user.data.user?.id)
        .select()
        .single();
    if (error) {
        console.error(
            "error getting role for user. Please relode page.",
            error
        );
    }

    return data;
}
