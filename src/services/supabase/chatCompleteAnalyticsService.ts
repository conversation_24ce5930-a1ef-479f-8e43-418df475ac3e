import OpenAI from "openai";
import Prompt from "../../models/Pompt";
import {
    AnalyticsItem,
    ComputedMetadata,
    ExtractedMetadata,
    ImageAnalysisData,
    SiteInfos,
} from "../../models/SiteInfos";
import supabaseAdminClient from "../../util/createClient";

const supabase = supabaseAdminClient();

async function saveChatCompleteAnalyticsItem(
    analyticsItem: AnalyticsItem
): Promise<number | null> {
    const { data, error } = await supabase
        .from("chat_complete_analytics")
        .insert(analyticsItem)
        .select("id")
        .single();

    if (error) {
        console.error("Error inserting chat complete analytics:", error);
        return null;
    }

    return data.id;
}

export async function createAnalyticsItem(
    analyticsItem: AnalyticsItem,
    html?: string
) {
    if (analyticsItem.ai_generated_message) {
        const analyticsItemId = await saveChatCompleteAnalyticsItem(
            analyticsItem
        );

        // if (analyticsItemId && html) {
        //     await uploadHtmlToSupabase(
        //         analyticsItemId,
        //         "chat-completion-analytics-htmls",
        //         html
        //     );
        // }

        return analyticsItemId;
    }
}

export function generateAnalyticsItem(
    prev_messages: OpenAI.ChatCompletionMessageParam[],
    siteInfos: SiteInfos,
    extractedMetadata: ExtractedMetadata,
    computedMetadata: ComputedMetadata,
    prompt: Prompt,
    ai_generated_message: string,
    summary?: { user: {}; assistant: {} },
    correctedOutput?: string,
    asset?: ImageAnalysisData
) {
    let metadata = {
        extractedMetadata: extractedMetadata,
        computedMetadata: computedMetadata,
        origin: siteInfos.origin,
        asset,
        moderatorInfo: siteInfos.metaData.moderatorInfo,
        customerInfo: siteInfos.metaData.customerInfo,
        moderatorNotes: siteInfos.metaData.moderatorNotes,
        customerNotes: siteInfos.metaData.customerNotes,
        sessionStart: siteInfos.metaData.sessionStart,
        ins: siteInfos.metaData.ins,
        outs: siteInfos.metaData.outs,
        type: siteInfos.metaData.type,
        importantNotes: siteInfos.metaData.importantNotes,
        alertBoxMessages: siteInfos.metaData.alertBoxMessages,
        chatId: siteInfos.metaData.chatId,
        moderatorId: siteInfos.metaData.moderatorId,
        customerId: siteInfos.metaData.customerId,
        summary:
            summary && summary.assistant && summary.user ? summary : undefined,
    };
    const analyticsItem: AnalyticsItem = {
        chat_complete_prompt_id: prompt.id!,
        prev_messages: prev_messages,
        origin_website: siteInfos.origin,
        language: "DE",
        ai_generated_message: ai_generated_message,
        corrected_output: correctedOutput,
        ai_model: prompt.ai_model_config.model!,
        metadata,
        account_id: siteInfos.accountId,
    };

    return analyticsItem;
}
