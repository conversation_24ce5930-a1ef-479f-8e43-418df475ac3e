import supabaseAdminClient from "../../util/createClient";
import ChokedRequest from "../../models/ChokedRequest";

export async function logChokedRequest(request: ChokedRequest) {
    const { data, error } = await supabaseAdminClient()
        .from("logs_choked_requests")
        .insert(request)
        .select();

    if (error) {
        console.error("Error logging choked request:", error);
    }
}
