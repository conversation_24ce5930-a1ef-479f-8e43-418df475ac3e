import * as deepl from "deepl-node";
import Glossary from "../../models/Glossary";
import supabaseAdminClient from "../../util/createClient";

export async function createGlossaryInDb(glossary: Glossary) {
    const { data, error } = await supabaseAdminClient()
        .from("glossaries")
        .insert(glossary)
        .select()
        .single();
}

export async function getGlossaryDb(id: string) {
    const { data, error } = await supabaseAdminClient()
        .from("glossaries")
        .select("*")
        .eq("id", id)
        .single();

    return data;
}

export async function findGlossaryDb(
    agency_id: string,
    source_lang: string,
    target_lang: string
) {
    const { data, error } = await supabaseAdminClient()
        .from("glossaries")
        .select("*")
        .eq("agency_id", agency_id)
        .eq("source_lang", source_lang)
        .eq("target_lang", target_lang)
        .single();

    return data;
}

export async function getGlossaryByAgencyAndLanguage(
    agencyId: string,
    target_lang: string,
    source_lang: string
) {
    const data = await findGlossaryDb(agencyId, source_lang, target_lang);

    if (!data) return undefined;

    const deeplAuthKey = process.env.DEEPL_API_KEY!;
    const translator = new deepl.Translator(deeplAuthKey);

    const glossaries = await translator.listGlossaries();
    const glossary = glossaries.find((glossary) => glossary.name == data.id);

    if (!glossary) return undefined;
    return glossary.glossaryId;
}
