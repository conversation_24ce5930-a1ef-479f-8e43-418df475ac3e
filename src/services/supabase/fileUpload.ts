import supabaseAdminClient from "../../util/createClient";

const supabase = supabaseAdminClient();

export async function uploadHtmlToSupabase(
    fileId: number,
    bucketName: string,
    htmlContent?: string
): Promise<string | null> {
    if (!htmlContent || htmlContent === "") {
        return null;
    }
    // is in base 64 format
    const htmlString = Buffer.from(
        htmlContent.split(",")[1],
        "base64"
    ).toString("utf-8");

    // Calculate the folder name based on the fileId
    const folderStart = Math.floor((fileId - 1) / 1000) * 1000 + 1;
    const folderEnd = folderStart + 999;
    const folderName = `${folderStart}-${folderEnd}`;

    // Create the full file path
    const fileName = `${fileId}.html`;
    const filePath = `${folderName}/${fileName}`;

    const htmlBlob = new Blob([htmlString], { type: "text/html" });

    // Upload the file to Supabase Storage
    const { data, error } = await supabase.storage
        .from(bucketName)
        .upload(filePath, htmlBlob, {
            contentType: "text/html",
            upsert: true, // This will replace the file if it already exists
        });

    if (error) {
        console.warn("Error uploading file:", error);
        return null;
    }

    // Get the public URL of the uploaded file
    const {
        data: { publicUrl },
    } = supabase.storage.from(bucketName).getPublicUrl(filePath);

    return publicUrl;
}
