import supabaseAdminClient from "../../util/createClient";
import { Mac } from "../../models/Mac";

export async function getMacDB(hostname: string): Promise<Mac | null> {
    const supabase = supabaseAdminClient();

    const { data, error } = await supabase
        .from("macs")
        .select("*")
        .eq("hostname", hostname)
        .single();

    if (error) {
        return null;
    }

    return data;
}

export async function getMacsDB(): Promise<Mac[]> {
    const supabase = supabaseAdminClient();

    const { data, error } = await supabase.from("macs").select("*");

    if (error) {
        console.error("Error fetching mac status:", error);
        return [];
    }

    return data;
}

export async function updateMacDB(mac: Mac) {
    const supabase = supabaseAdminClient();

    const { data, error } = await supabase
        .from("macs")
        .upsert(mac)
        .eq("hostname", mac.hostname);

    if (error) {
        console.error("Error updating mac status:", error);
        throw error;
    }
}
