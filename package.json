{"name": "chat-translator-backend", "version": "1.0.0", "description": "", "main": "src/index.js", "scripts": {"build": "npx tsc && npm run copy-assets", "copy-assets": "mkdir -p dist/services/googlePlaces dist/util && cp src/services/googlePlaces/cities_plz.json dist/services/googlePlaces/ && cp src/services/googlePlaces/citiesToSearch.csv dist/services/googlePlaces/ && cp src/util/tw-asa-1.csv dist/util/ && cp src/util/tw-icebreaker.csv dist/util/ && cp src/util/tw-asa.csv dist/util/", "start": "npm run build && node dist/index.js", "dev": "nodemon --exec ts-node --transpile-only src/index.ts", "install-all": "npm install --include=dev"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google/genai": "^0.8.0", "@google/generative-ai": "^0.24.0", "@logsnag/node": "^1.0.1", "@sentry/node": "^9.33.0", "@sentry/profiling-node": "^9.33.0", "@supabase/supabase-js": "^2.41.1", "@types/csv-parse": "^1.2.5", "@types/lodash": "^4.17.7", "cors": "^2.8.5", "csv-parse": "^5.6.0", "dayjs": "^1.11.12", "deepl-node": "^1.11.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-xss-sanitizer": "^1.1.8", "fast-levenshtein": "^3.0.0", "fs": "^0.0.1-security", "groq-sdk": "^0.15.0", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "logsnag": "^1.0.0", "node-cron": "^3.0.3", "node-telegram-bot-api": "^0.66.0", "openai": "^5.8.2", "sharp": "^0.33.5", "together-ai": "^0.16.0", "util": "^0.12.5", "uuid": "^9.0.1", "zod": "^3.23.8"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fast-levenshtein": "^0.0.4", "@types/jsonwebtoken": "^9.0.6", "@types/mime": "3.0.0", "@types/node": "^20.10.6", "@types/node-cron": "^3.0.11", "@types/node-telegram-bot-api": "^0.64.7", "@types/uuid": "^9.0.8", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}